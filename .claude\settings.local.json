{"permissions": {"allow": ["<PERSON><PERSON>(mv:*)", "Bash(find:*)", "Bash(grep:*)", "Bash(ls:*)", "Bash(npm run build:*)", "Bash(npx tsc:*)", "<PERSON><PERSON>(python:*)", "Bash(cd /mnt/d/diannao/wendang/github/OperationDelta/OperationDelta-Go-API)", "Bash(go build)", "Bash(rm:*)", "Bash(go mod:*)", "Bash(go build:*)", "<PERSON><PERSON>(go run:*)", "<PERSON><PERSON>(timeout:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(true)", "Bash(go get:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(sed:*)", "Bash(whereis:*)", "<PERSON><PERSON>(go clean:*)", "<PERSON><PERSON>(mysql:*)", "Bash(cp:*)", "WebFetch(domain:goframe.org)", "Bash(php:*)", "Bash(./think --version)", "<PERSON><PERSON>(cat:*)", "<PERSON><PERSON>(echo:*)", "Bash(pip3 install:*)", "WebFetch(domain:echarts.apache.org)", "Bash(npm install:*)", "mcp__ide__getDiagnostics"], "deny": []}}