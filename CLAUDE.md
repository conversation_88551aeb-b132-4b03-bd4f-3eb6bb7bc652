# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository. Ultrathink

## Important Instructions
- 请永远回复中文 (Always respond in Chinese)
- 你的代码修改应尽可能局限在当前任务范围内。除非绝对必要，否则不要创建新的辅助函数或进行范围外的重构。保持代码简洁和最小化，避免增加不必要的认知复杂度。
- 除了小程序其他的页面图片可以用SVG来代替。
- 处理问题的时候，先看用户发来的信息，自己仔细思考下没问题在进行更改避免改完之后不对，报错等问题。
- 你不需要测试代码我自己来测试

## 项目架构概览

这是一个多端《三角洲行动》游戏数据管理平台，包含以下主要模块：

### 核心模块
- **OperationDelta-Admin_Api_new**: 后端API (ThinkPHP 8.1 + MySQL + Redis)
- **OperationDelta-Web**: Web前端 (Vue 3 + TypeScript + Naive UI)
- **OperationDelta-Desktop**: 桌面应用 (Electron + Vue 3 + Element Plus) 
- **OperationDelta-xcx**: 微信小程序 (原生开发)
- **OperationDelta-Bot**: QQ机器人 (Python 3 + WebSocket)
- **scripts_new**: 数据处理脚本 (Python 3)

### 数据架构
- 主数据库: MySQL 8.0 (物品数据、价格历史、用户管理)
- 缓存层: Redis (热点数据缓存、排行榜缓存)
- 数据源: 游戏解包数据 + 实时爬虫数据
- 数据表：database\dfnew.sql

## 关键技术要点

### API开发 (ThinkPHP)
- 控制器位置: `app/api/controller/`
- 服务层: `app/api/controller/service/`
- 模型位置: `app/common/model/`
- 缓存使用: `cache()` 助手函数，默认Redis存储
- 验证器: `app/common/validate/` 目录下
- 中间件: `app/middleware/` 认证和限流
- 数据表的ba_是我们的自带的前缀，这个要注意！

### 前端开发 (Vue 3)
- 组件位置: `src/components/`
- 页面路由: `src/views/`
- 状态管理: Pinia store (`src/stores/`)
- API调用: `src/api/` 统一管理
- 工具函数: `src/utils/`
- 前端开发的时候要注意 优化移动端体验，响应式体验，必要条件下可以单独创建移动端的组件
- 前端开发的时候要注意必须兼容主题 暗黑白色的。

### 数据库结构
- 物品表: 存储游戏物品基础信息和属性
- 价格历史表: 记录物品价格变动轨迹
- 用户表: 用户账户和权限管理
- 收藏表: 用户收藏的物品关联
- 排行榜缓存表: 预计算的热门数据

### 缓存策略
- Redis Key规范: `delta:物品ID:类型` 格式
- TTL设置: 热点数据30分钟，普通数据2小时
- 缓存更新: 数据变更时自动失效相关缓存
- 分布式锁: 防止缓存击穿和并发问题

## 重要说明
