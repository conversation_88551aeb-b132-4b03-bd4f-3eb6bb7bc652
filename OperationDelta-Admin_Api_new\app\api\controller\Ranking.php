<?php
declare(strict_types=1);

namespace app\api\controller;

use think\facade\Db;
use think\facade\Cache;
use think\Request;
use think\facade\Log;
use app\api\controller\BaseApiController;
use app\api\service\CacheManager;
use app\api\service\RankingService;
use app\api\service\ResponseAdapter;
use think\App;

/**
 * 价格排行榜相关API控制器
 * 
 * @api {get} /api/ranking/list 获取价格排行榜数据
 * @apiVersion 1.0.0
 * @apiName getRankingList
 * @apiGroup Ranking
 * 
 * @apiParam {String} [type=highest_price] 排序类型（highest_price/lowest_price/increase_percentage/decrease_percentage/price_change_absolute/price_change_max/price_change_min/highest_24h/lowest_24h/volatility/trend_up/trend_down/week_change）
 * @apiParam {String} [time_range=day] 时间范围（hour/day/week/month）
 * @apiParam {Number} [page=1] 页码
 * @apiParam {Number} [page_size=20] 每页数量
 * @apiParam {Number} [grade] 物品等级筛选
 * @apiParam {String} [item_type] 物品类型筛选
 * @apiParam {Number} [min_price] 最小价格筛选
 * @apiParam {Number} [max_price] 最大价格筛选
 * 
 * @apiSuccess {Number} code 状态码（1成功，0失败）
 * @apiSuccess {String} msg 提示信息
 * @apiSuccess {Number} time 时间戳
 * @apiSuccess {Object} data 返回数据
 * @apiSuccess {Array} data.list 列表数据
 * @apiSuccess {Number} data.total 总记录数
 * @apiSuccess {Number} data.page 当前页码
 * @apiSuccess {Number} data.page_size 每页数量
 * 
 * 重构记录:
 * 2025-07-27: 智能缓存策略重构
 *   - 引入智能缓存管理系统，支持价格数据整点2分钟更新机制
 *   - 排行榜数据使用价格缓存策略，保证数据时效性
 *   - 保持100%功能兼容性，响应格式完全不变
 */
class Ranking extends BaseApiController
{
    // 新增钥匙卡排行榜接口无需登录
    protected array $noNeedLogin = ['getRankingList', 'getKeycardRanking','getBulletRanking','getBulletPackageRanking','getBulletPrices'];
    protected array $noNeedPermission = [];
    private string $currentType = 'highest_price';
    
    
    private CacheManager $cacheManager;
    private RankingService $rankingService;

    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->cacheManager = new CacheManager();
        $this->rankingService = new RankingService();
        // $this->performanceMonitor = new PerformanceMonitor();  // 暂时禁用
    }

    /**
     * 获取价格排行榜数据
     *
     * @param Request $request
     * @return \think\Response
     */
    public function getRankingList(Request $request): \think\Response
    {
        try {
            // 参数验证
            $params = $this->validateAndGetParams($request);
            $this->currentType = $params['type'];


            // 缓存查询
            $cacheKey = 'ranking_list:' . md5(json_encode($params));

            $result = $this->cacheManager->remember($cacheKey, CacheManager::TYPE_PRICE_DATA, function() use ($params) {
                return $this->fetchRankingListData($params);
            });

            // 使用 ResponseAdapter 返回响应
            return ResponseAdapter::success('获取排行榜数据成功', $result);

        } catch (\Exception $e) {
            Log::error('getRankingList error: ' . $e->getMessage() . "\n" . $e->getTraceAsString());
            return ResponseAdapter::error('获取排行榜数据失败：' . $e->getMessage());
        }
    }

    /**
     * 获取排行榜数据的核心逻辑
     */
    private function fetchRankingListData(array $params): array
    {
        $startTime = microtime(true);

        // 获取列表数据
        try {
            $list = $this->getListData($params);
        } catch (\Throwable $listEx) {
            Log::error('获取列表数据方法失败: ' . $listEx->getMessage());
            throw $listEx;
        }

        // 获取总记录数
        try {
            $total = $this->getTotal($params);
        } catch (\Throwable $totalEx) {
            Log::error('获取总记录数方法失败: ' . $totalEx->getMessage());
            $total = count($list);
        }

        // 价格变化数据处理
        try {
            $list = $this->optimizedProcessPriceChanges($list, $params['time_range']);
        } catch (\Throwable $processEx) {
            Log::error('价格变化数据处理失败: ' . $processEx->getMessage());
        }

        // 计算总耗时
        $queryTime = round(microtime(true) - $startTime, 4);

        // 获取扩展统计信息
        $extendedStats = $this->getExtendedPriceStats($list);

        // 构建返回数据
        return [
            'list' => $list,
            'total' => $total,
            'page' => $params['page'],
            'page_size' => $params['page_size'],
            'query_time' => $queryTime,
            'extended_stats' => $extendedStats
        ];
    }

    /**
     * 获取列表数据
     *
     * @param array $params
     * @return array
     */
    private function getListData(array $params): array
    {
        try {
            $startTime = microtime(true);
            
            // 构建缓存键
            $cacheKey = 'ranking:list_data:' . md5(json_encode($params));
            
            // 构建查询对象
            $queryBuildStartTime = microtime(true);
            $query = $this->buildBaseQuery($params);
            $queryBuildTime = round(microtime(true) - $queryBuildStartTime, 4);
            
            // 先查询总记录数，以便进行准确的分页
            // 使用getTotal方法，它现在会使用相同的查询条件
            $total = $this->getTotal($params);
            
            // 修正页码，确保不超出实际范围
            $page = max(1, intval($params['page']));
            $pageSize = min(intval($params['page_size']), 100); // 最多返回100条记录
            $maxPage = max(1, ceil($total / $pageSize));
            
            if ($page > $maxPage) {
                $page = $maxPage;
            }
            
            // 计算正确的偏移量
            $offset = ($page - 1) * $pageSize;
            
            // 记录分页信息
            
            // 关闭查询日志和分析，提高性能
            $query->fetchSql(false);
            
            // 执行查询
            $queryExecStartTime = microtime(true);
            
            // 记录实际执行的SQL，用于调试
            try {
                $debugSql = (clone $query)->limit($offset, $pageSize)->fetchSql(true)->select();
                // 限制SQL长度
                if (strlen($debugSql) > 500) {
                    $debugSql = substr($debugSql, 0, 497) . '...';
                }
            } catch (\Throwable $sqlEx) {
                Log::warning('无法获取调试SQL: ' . $sqlEx->getMessage());
            }
            
            // 真正执行查询
            $list = $query->limit($offset, $pageSize)->select()->toArray();
            $queryExecTime = round(microtime(true) - $queryExecStartTime, 4);
            
            // 使用CacheManager缓存结果数据
            $cachedList = $this->cacheManager->remember($cacheKey, CacheManager::TYPE_PRICE_DATA, function() use ($list) {
                return $list;
            });
            
            $totalTime = round(microtime(true) - $startTime, 4);
            
            return $list;
        } catch (\Throwable $e) {
            // 记录详细错误并重新抛出
            Log::error('获取列表数据失败: ' . $e->getMessage() . ' in ' . $e->getFile() . ' line ' . $e->getLine());
            Log::error('获取列表数据堆栈: ' . $e->getTraceAsString());
            Log::error('参数信息: ' . json_encode($params, JSON_UNESCAPED_UNICODE));
            
            // 抛出异常给上层处理
            throw $e;
        }
    }

    /**
     * 获取总记录数 - 优化版本
     *
     * @param array $params
     * @return int
     */
    private function getTotal(array $params): int
    {
        try {
            $startTime = microtime(true);
            
            // 构建缓存键
            $cacheKey = 'ranking:total:' . md5(json_encode($params));
            
            // 使用CacheManager尝试从缓存获取数据
            $cachedTotal = $this->cacheManager->remember($cacheKey, CacheManager::TYPE_PRICE_DATA, function() {
                return null;
            });
            if ($cachedTotal !== null) {
                return (int)$cachedTotal;
            }
            
            // 获取时间范围
            $timeRange = $params['time_range'] ?? 'day';

            // 使用新的扩展价格表进行计数查询
            $query = Db::name('sjz_items')
                ->alias('i')
                ->where('i.delete_time', null)
                ->join('sjz_latest_prices lp', 'i.object_id = lp.object_id', 'INNER')
                ->where('lp.current_price', '>', 0);

            // 根据时间范围添加相应的条件
            switch ($timeRange) {
                case 'hour':
                    $query->where('lp.price_1h_ago', 'not null');
                    break;
                case 'day':
                    $query->where('lp.price_24h_ago', 'not null');
                    break;
                case 'week':
                    $query->where('lp.price_7d_ago', 'not null');
                    break;
                case 'month':
                    $query->where('lp.price_30d_ago', 'not null');
                    break;
                default:
                    $query->where('lp.price_24h_ago', 'not null');
                    break;
            }
            
            // 应用筛选条件
            if (!empty($params['grade']) && is_numeric($params['grade'])) {
                $query->where('i.grade', intval($params['grade']));
            }
            
            if (!empty($params['item_type'])) {
                $query->join('sjz_item_categories c', 'i.category_id = c.id', 'INNER')
                    ->where(function($q) use ($params) {
                        $q->where('c.primary_class', $params['item_type'])
                          ->whereOr('c.second_class', $params['item_type']);
                    });
            }
            
            if (isset($params['min_price']) && is_numeric($params['min_price']) && $params['min_price'] > 0) {
                $query->where('lp.current_price', '>=', floatval($params['min_price']));
            }

            if (isset($params['max_price']) && is_numeric($params['max_price']) && $params['max_price'] > 0) {
                $query->where('lp.current_price', '<=', floatval($params['max_price']));
            }
            
            // 简化计数查询
            $queryStart = microtime(true);
            $total = (int)$query->count('i.object_id');
            $queryDuration = round(microtime(true) - $queryStart, 4);
            
            // 查询完成
            
            // 缓存结果
            $this->cacheManager->remember($cacheKey . '_result', CacheManager::TYPE_PRICE_DATA, function() use ($total) {
                return $total;
            });
            
            $executeTime = round(microtime(true) - $startTime, 4);
            
            return (int)$total;
        } catch (\Throwable $e) {
            Log::error('计数查询失败: ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * 构建基础查询
     *
     * @param array $params
     * @return \think\db\Query
     */
    private function buildBaseQuery(array $params): \think\db\Query
    {
        try {
            $startTime = microtime(true);
            
            // 不直接缓存Query对象，因为它们包含资源句柄，不能被序列化
            // 但可以缓存构建过程中的复杂条件或中间结果
            
            // 直接构建基础查询
            $query = Db::name('sjz_items')
                ->alias('i')
                ->join('sjz_item_categories c', 'i.category_id = c.id', 'left')
                ->where('i.delete_time', null);
            
            $buildTime = round(microtime(true) - $startTime, 4);
                
            // 不再检查is_active字段，直接应用条件
            $filterStartTime = microtime(true);
            $this->applyFilters($query, $params);
            $filterTime = round(microtime(true) - $filterStartTime, 4);
            
            $timeRangeStartTime = microtime(true);
            $this->applyTimeRangeConditions($query, $params['time_range']);
            $timeRangeTime = round(microtime(true) - $timeRangeStartTime, 4);
            
            $fieldStartTime = microtime(true);
            $this->applyFieldSelection($query, $params['time_range']);
            $fieldTime = round(microtime(true) - $fieldStartTime, 4);
            
            $sortStartTime = microtime(true);
            $this->applySorting($query, $this->getCurrentType());
            $sortTime = round(microtime(true) - $sortStartTime, 4);
            
            $totalTime = round(microtime(true) - $startTime, 4);
            
            return $query;
        } catch (\Throwable $e) {
            Log::error('构建查询对象失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            // 返回最简单的查询，保证程序不中断
            return Db::name('sjz_items')->alias('i');
        }
    }

    /**
     * 优化的价格变化数据处理 - 使用扩展字段
     *
     * @param array $list
     * @param string $timeRange
     * @return array
     */
    private function optimizedProcessPriceChanges(array $list, string $timeRange): array
    {
        $startTime = microtime(true);

        // 批量处理，减少单个操作
        foreach ($list as &$item) {
            // 确保价格数据类型转换
            $currentPrice = (float)($item['current_price'] ?? 0);

            // 计算单位价格
            $width = (float)($item['width'] ?? 1);
            $length = (float)($item['length'] ?? 1);
            $area = ($width > 0 && $length > 0) ? ($width * $length) : 1;
            $item['unit_price'] = round($currentPrice / $area, 2);

            // 使用预计算的价格变化数据
            $item['price_change'] = (float)($item['price_change_24h'] ?? 0);
            $item['price_change_percentage'] = (float)($item['price_change_24h_percent'] ?? 0);

            // 使用预计算的趋势数据，如果没有则根据百分比计算
            if (!empty($item['price_trend'])) {
                $item['trend'] = match($item['price_trend']) {
                    '上涨' => 'up',
                    '下跌' => 'down',
                    '平稳' => 'stable',
                    default => 'stable'
                };
            } else {
                $priceChangePercentage = $item['price_change_percentage'];
                $item['trend'] = $priceChangePercentage > 0.1 ? 'up' : ($priceChangePercentage < -0.1 ? 'down' : 'stable');
            }

            // 添加扩展价格信息
            $item['highest_price_24h'] = (float)($item['highest_price_24h'] ?? $currentPrice);
            $item['lowest_price_24h'] = (float)($item['lowest_price_24h'] ?? $currentPrice);
            $item['highest_price_7d'] = (float)($item['highest_price_7d'] ?? $currentPrice);
            $item['lowest_price_7d'] = (float)($item['lowest_price_7d'] ?? $currentPrice);
            $item['avg_price_24h'] = (float)($item['avg_price_24h'] ?? $currentPrice);
            $item['volatility_24h'] = (float)($item['volatility_24h'] ?? 0);

            // 根据时间范围添加对应的价格变化信息
            switch ($timeRange) {
                case 'hour':
                    $item['previous_price'] = (float)($item['price_1h_ago'] ?? 0);
                    break;
                case 'week':
                    $item['previous_price'] = (float)($item['price_7d_ago'] ?? 0);
                    $item['price_change_7d'] = (float)($item['price_change_7d'] ?? 0);
                    $item['price_change_7d_percentage'] = (float)($item['price_change_7d_percent'] ?? 0);
                    break;
                case 'month':
                    $item['previous_price'] = (float)($item['price_30d_ago'] ?? 0);
                    break;
                default:
                    $item['previous_price'] = (float)($item['price_24h_ago'] ?? 0);
                    break;
            }

            // 确保数据类型
            $item['current_price'] = $currentPrice;
        }

        $executeTime = round(microtime(true) - $startTime, 4);

        return $list;
    }

    /**
     * 处理价格变化数据
     *
     * @param array $list
     * @param string $timeRange
     * @return array
     */
    private function processPriceChanges(array $list, string $timeRange): array
    {
        $startTime = microtime(true);
        
        // 构建缓存键，基于列表数据和时间范围
        $dataHash = md5(json_encode($list) . $timeRange);
        $cacheKey = 'ranking:price_changes:' . $dataHash;
        
        // 使用CacheManager尝试从缓存获取数据
        $cachedData = $this->cacheManager->remember($cacheKey, CacheManager::TYPE_PRICE_DATA, function() {
            return null; // 如果缓存不存在，返回null继续执行处理
        });
        if ($cachedData !== null) {
            return $cachedData;
        }
        
        // 用于记录处理情况的计数器
        $stats = [
            'total' => count($list),
            'with_previous_price' => 0,
            'without_previous_price' => 0,
            'up_trend' => 0,
            'down_trend' => 0,
            'stable_trend' => 0
        ];
        
        // 批量处理所有记录
        foreach ($list as &$item) {
            // 转换价格数据为浮点数
            $currentPrice = isset($item['current_price']) ? (float)$item['current_price'] : 0;
            $previousPrice = isset($item['previous_price']) ? (float)$item['previous_price'] : 0;
            
            // 确保所有价格数据都是浮点数
            if (isset($item['lowest_price'])) $item['lowest_price'] = (float)$item['lowest_price'];
            if (isset($item['highest_price'])) $item['highest_price'] = (float)$item['highest_price'];
            if (isset($item['average_price'])) $item['average_price'] = (float)$item['average_price'];
            
            // 计算单格价格(单位价格)
            $width = isset($item['width']) ? (float)$item['width'] : 1;
            $length = isset($item['length']) ? (float)$item['length'] : 1;
            
            // 避免除以零的情况
            $area = ($width > 0 && $length > 0) ? ($width * $length) : 1;
            $unitPrice = $currentPrice / $area;
            
            // 加入单位价格字段
            $item['unit_price'] = round($unitPrice, 2);
            
            // 处理价格变化
            if ($previousPrice <= 0) {
                $stats['without_previous_price']++;
                $priceChange = 0;
                $priceChangePercentage = 0;
                $trend = 'stable';
            } else {
                $stats['with_previous_price']++;
                // 计算价格变化
                $priceChange = $currentPrice - $previousPrice;
                
                // 计算变化百分比
                $priceChangePercentage = ($priceChange / $previousPrice) * 100;
                
                // 确定价格趋势，添加0.1%的阈值
                if ($priceChangePercentage > 0.1) {
                    $trend = 'up';
                    $stats['up_trend']++;
                } elseif ($priceChangePercentage < -0.1) {
                    $trend = 'down';
                    $stats['down_trend']++;
                } else {
                    $trend = 'stable';
                    $stats['stable_trend']++;
                }
            }
            
            // 四舍五入保留两位小数
            $priceChange = round($priceChange, 2);
            $priceChangePercentage = round($priceChangePercentage, 2);
            
            // 更新价格字段
            $item['current_price'] = $currentPrice;
            $item['previous_price'] = $previousPrice;
            $item['price_change'] = $priceChange;
            $item['price_change_percentage'] = $priceChangePercentage;
            $item['trend'] = $trend;
            
            // 跳过小时查询的额外计算
            if ($timeRange === 'hour') {
                continue;
            }
            
            // 计算价格对比数据
            $this->calculatePriceComparisons($item);
        }
        
        // 使用CacheManager缓存处理结果
        $this->cacheManager->remember($cacheKey . '_processed', CacheManager::TYPE_PRICE_DATA, function() use ($list) {
            return $list;
        });
        
        $executeTime = round(microtime(true) - $startTime, 4);
        $avgPerItem = $executeTime > 0 && count($list) > 0 ? round($executeTime / count($list), 6) : 0;
        
        
        return $list;
    }

    /**
     * 获取扩展价格统计信息
     *
     * @param array $list
     * @return array
     */
    private function getExtendedPriceStats(array $list): array
    {
        if (empty($list)) {
            return [];
        }

        $stats = [
            'total_items' => count($list),
            'avg_current_price' => 0,
            'avg_volatility' => 0,
            'trend_distribution' => [
                'up' => 0,
                'down' => 0,
                'stable' => 0
            ],
            'price_range_24h' => [
                'min' => null,
                'max' => null,
                'avg' => 0
            ]
        ];

        $totalPrice = 0;
        $totalVolatility = 0;
        $volatilityCount = 0;
        $priceRanges = [];

        foreach ($list as $item) {
            // 累计当前价格
            $currentPrice = (float)($item['current_price'] ?? 0);
            $totalPrice += $currentPrice;

            // 累计波动率
            $volatility = (float)($item['volatility_24h'] ?? 0);
            if ($volatility > 0) {
                $totalVolatility += $volatility;
                $volatilityCount++;
            }

            // 统计趋势分布
            $trend = $item['trend'] ?? 'stable';
            if (isset($stats['trend_distribution'][$trend])) {
                $stats['trend_distribution'][$trend]++;
            }

            // 统计24小时价格范围
            $highest24h = (float)($item['highest_price_24h'] ?? 0);
            $lowest24h = (float)($item['lowest_price_24h'] ?? 0);
            if ($highest24h > 0 && $lowest24h > 0) {
                $priceRanges[] = $highest24h - $lowest24h;
            }
        }

        // 计算平均值
        $stats['avg_current_price'] = round($totalPrice / count($list), 2);
        $stats['avg_volatility'] = $volatilityCount > 0 ? round($totalVolatility / $volatilityCount, 4) : 0;

        if (!empty($priceRanges)) {
            $stats['price_range_24h']['min'] = round(min($priceRanges), 2);
            $stats['price_range_24h']['max'] = round(max($priceRanges), 2);
            $stats['price_range_24h']['avg'] = round(array_sum($priceRanges) / count($priceRanges), 2);
        }

        return $stats;
    }

    /**
     * 验证并获取请求参数
     *
     * @param Request $request
     * @return array
     */
    private function validateAndGetParams(Request $request): array
    {
        $params = $request->param();
        $type = $params['type'] ?? 'highest_price';
        
        // 记录接收到的排序类型参数
        
        return [
            'type' => $type,
            'time_range' => $params['time_range'] ?? 'day',
            'page' => (int)($params['page'] ?? 1),
            'page_size' => (int)($params['page_size'] ?? 20),
            'grade' => (int)($params['grade'] ?? 0),
            'item_type' => $params['item_type'] ?? '',
            'min_price' => isset($params['min_price']) ? (float)$params['min_price'] : null,
            'max_price' => isset($params['max_price']) ? (float)$params['max_price'] : null,
        ];
    }

    /**
     * 应用筛选条件
     *
     * @param \think\db\Query $query
     * @param array $params
     * @return void
     */
    private function applyFilters(\think\db\Query $query, array $params): void
    {
        // 应用等级筛选
        if (!empty($params['grade']) && is_numeric($params['grade'])) {
            $query->where('i.grade', intval($params['grade']));
        }

        // 应用物品类型筛选
        if (!empty($params['item_type'])) {
            $query->where(function($q) use ($params) {
                $q->where('c.primary_class', $params['item_type'])
                  ->whereOr('c.second_class', $params['item_type']);
            });
        }

        // 应用价格范围筛选 - 使用扩展价格表
        if (isset($params['min_price']) && is_numeric($params['min_price']) && $params['min_price'] > 0) {
            $query->where('lp.current_price', '>=', floatval($params['min_price']));
        }

        if (isset($params['max_price']) && is_numeric($params['max_price']) && $params['max_price'] > 0) {
            $query->where('lp.current_price', '<=', floatval($params['max_price']));
        }
    }

    /**
     * 应用时间范围条件 - 使用新的扩展价格字段
     *
     * @param \think\db\Query $query
     * @param string $timeRange
     * @return void
     */
    private function applyTimeRangeConditions(\think\db\Query $query, string $timeRange): void
    {
        // 使用新的 sjz_latest_prices 表，该表已包含所有扩展价格维度
        $query->join('sjz_latest_prices lp', 'i.object_id = lp.object_id', 'INNER');

        // 根据时间范围应用不同的筛选条件
        switch ($timeRange) {
            case 'hour':
                // 1小时范围：确保有1小时前的价格数据
                $query->where('lp.price_1h_ago', 'not null');
                break;

            case 'day':
                // 24小时范围：确保有24小时前的价格数据
                $query->where('lp.price_24h_ago', 'not null');
                break;

            case 'week':
                // 7天范围：确保有7天前的价格数据
                $query->where('lp.price_7d_ago', 'not null');
                break;

            case 'month':
                // 30天范围：确保有30天前的价格数据
                $query->where('lp.price_30d_ago', 'not null');
                break;

            default:
                // 默认使用24小时数据
                $query->where('lp.price_24h_ago', 'not null');
                break;
        }

        // 确保当前价格大于0
        $query->where('lp.current_price', '>', 0);

    }

    /**
     * 应用字段选择 - 使用扩展价格字段
     *
     * @param \think\db\Query $query
     * @param string $timeRange
     * @return void
     */
    private function applyFieldSelection(\think\db\Query $query, string $timeRange): void
    {
        // 基础字段
        $fields = [
            'i.object_id',
            'i.object_name as name',
            'i.pic as image_url',
            'i.grade',
            'i.width',
            'i.length',
            'c.primary_class',
            'c.second_class',
            // 当前价格和基础价格信息
            'lp.current_price',
            'lp.price_24h_ago',
            'lp.price_7d_ago',
            'lp.price_30d_ago',
            'lp.price_1h_ago',
            // 价格变化统计
            'lp.price_change_24h',
            'lp.price_change_24h_percent',
            'lp.price_change_7d',
            'lp.price_change_7d_percent',
            // 价格极值
            'lp.highest_price_24h',
            'lp.lowest_price_24h',
            'lp.highest_price_7d',
            'lp.lowest_price_7d',
            // 统计指标
            'lp.avg_price_24h',
            'lp.price_trend',
            'lp.volatility_24h',
            // 时间戳
            'lp.last_update_timestamp'
        ];

        // 根据时间范围添加对应的前一周期价格字段
        switch ($timeRange) {
            case 'hour':
                $fields[] = 'lp.price_1h_ago as previous_price';
                break;
            case 'day':
                $fields[] = 'lp.price_24h_ago as previous_price';
                break;
            case 'week':
                $fields[] = 'lp.price_7d_ago as previous_price';
                break;
            case 'month':
                $fields[] = 'lp.price_30d_ago as previous_price';
                break;
            default:
                $fields[] = 'lp.price_24h_ago as previous_price';
                break;
        }

        // 设置查询字段
        $query->field($fields);

    }
    
    /**
     * 检查字段是否存在
     *
     * @param string $fieldName
     * @return bool
     */
    private function fieldExists(string $fieldName): bool
    {
        // 这里简化处理，实际应该根据数据库表结构检查
        // 当前实现中，默认这些字段不存在，它们将在后续数据处理中计算或设置默认值
        $existingFields = [
            'i.object_id', 'i.object_name', 'i.pic', 'i.grade',
            'c.primary_class', 'c.second_class'
        ];
        
        return in_array($fieldName, $existingFields);
    }

    /**
     * 应用排序 - 使用扩展价格字段
     *
     * @param \think\db\Query $query
     * @param string $sortType
     * @return void
     */
    private function applySorting(\think\db\Query $query, string $sortType): void
    {

        // 使用新的扩展价格字段进行排序
        switch ($sortType) {
            case 'highest_price':
                $query->order('lp.current_price', 'desc');
                break;

            case 'lowest_price':
                $query->order('lp.current_price', 'asc');
                break;

            case 'increase_percentage':
                // 使用预计算的24小时价格变化百分比
                $query->where('lp.price_change_24h_percent', '>', 0)
                      ->order('lp.price_change_24h_percent', 'desc');
                break;

            case 'decrease_percentage':
                // 使用预计算的24小时价格变化百分比（负值）
                $query->where('lp.price_change_24h_percent', '<', 0)
                      ->order('lp.price_change_24h_percent', 'asc');
                break;

            case 'price_change_absolute':
                // 使用预计算的24小时价格变化绝对值
                $query->where('lp.price_change_24h', 'not null')
                      ->orderRaw('ABS(lp.price_change_24h) DESC');
                break;

            case 'price_change_max':
                // 最大价格上涨
                $query->where('lp.price_change_24h', '>', 0)
                      ->order('lp.price_change_24h', 'desc');
                break;

            case 'price_change_min':
                // 最大价格下跌
                $query->where('lp.price_change_24h', '<', 0)
                      ->order('lp.price_change_24h', 'asc');
                break;

            case 'highest_24h':
                // 24小时内最高价排序
                $query->order('lp.highest_price_24h', 'desc');
                break;

            case 'lowest_24h':
                // 24小时内最低价排序
                $query->order('lp.lowest_price_24h', 'asc');
                break;

            case 'volatility':
                // 按波动率排序
                $query->where('lp.volatility_24h', 'not null')
                      ->order('lp.volatility_24h', 'desc');
                break;

            case 'trend_up':
                // 上涨趋势
                $query->where('lp.price_trend', '上涨')
                      ->order('lp.price_change_24h_percent', 'desc');
                break;

            case 'trend_down':
                // 下跌趋势
                $query->where('lp.price_trend', '下跌')
                      ->order('lp.price_change_24h_percent', 'asc');
                break;

            case 'week_change':
                // 7天价格变化
                $query->where('lp.price_change_7d_percent', 'not null')
                      ->order('lp.price_change_7d_percent', 'desc');
                break;

            default:
                $query->order('lp.current_price', 'desc');
                break;
        }

    }

    /**
     * 获取当前排序类型
     *
     * @return string
     */
    private function getCurrentType(): string
    {
        return $this->currentType;
    }

    /**
     * 计算价格对比数据
     *
     * @param array &$item 物品数据引用
     * @return void
     */
    private function calculatePriceComparisons(array &$item): void
    {
        $currentPrice = (float)$item['current_price']; 
        
        // 与平均价格比较
        $averagePrice = isset($item['average_price']) ? (float)$item['average_price'] : $currentPrice;
        if ($averagePrice <= 0) $averagePrice = $currentPrice;
        
        $avgDifference = $currentPrice - $averagePrice;
        $avgPercentage = $averagePrice > 0 ? ($avgDifference / $averagePrice) * 100 : 0;
        
        // 与最高价格比较
        $highestPrice = isset($item['highest_price']) ? (float)$item['highest_price'] : $currentPrice;
        if ($highestPrice <= 0) $highestPrice = $currentPrice;
        
        $highDifference = $currentPrice - $highestPrice;
        $highPercentage = $highestPrice > 0 ? ($highDifference / $highestPrice) * 100 : 0;
        
        // 与最低价格比较
        $lowestPrice = isset($item['lowest_price']) ? (float)$item['lowest_price'] : $currentPrice;
        if ($lowestPrice <= 0) $lowestPrice = $currentPrice;
        
        $lowDifference = $currentPrice - $lowestPrice;
        $lowPercentage = $lowestPrice > 0 ? ($lowDifference / $lowestPrice) * 100 : 0;
        
        // 一次性设置所有比较数据，减少数组操作
        $item['compare_to_average'] = [
            'difference' => round($avgDifference, 2),
            'percentage' => round($avgPercentage, 2)
        ];
        
        $item['compare_to_highest'] = [
            'difference' => round($highDifference, 2),
            'percentage' => round($highPercentage, 2)
        ];
        
        $item['compare_to_lowest'] = [
            'difference' => round($lowDifference, 2),
            'percentage' => round($lowPercentage, 2)
        ];
    }

    /**
     * 获取钥匙卡排行榜数据
     *
     * @param Request $request
     * @return \think\Response
     */
    public function getKeycardRanking(Request $request): \think\Response
    {
        $startTime = microtime(true);
        
        try {
            // 参数解析和验证
            $page = max(1, min(1000, (int)$request->param('page', 1))); // 限制最大页数
            $pageSize = max(1, min(100, (int)$request->param('page_size', 20))); // 限制每页数量
            $sortParam = $request->param('sort', 'current_price_desc');
            
            // 获取筛选参数
            $grade = $request->param('grade', null);
            $minPrice = $request->param('min_price', null);
            $maxPrice = $request->param('max_price', null);
            
            // 构建筛选参数数组
            $filters = [
                'grade' => $grade !== null ? (int)$grade : null,
                'min_price' => $minPrice !== null ? (float)$minPrice : null,
                'max_price' => $maxPrice !== null ? (float)$maxPrice : null,
            ];

            // 优化缓存键，加入版本号和筛选参数防止缓存污染
            $filterHash = md5(json_encode($filters));
            $cacheKey = sprintf('keycard_ranking_v3:%d:%d:%s:%s', $page, $pageSize, $sortParam, $filterHash);

            $responseData = $this->cacheManager->remember($cacheKey, CacheManager::TYPE_PRICE_DATA, function() use ($page, $pageSize, $sortParam, $filters) {
                $serviceStartTime = microtime(true);
                $result = $this->rankingService->getKeycardRankingData($page, $pageSize, $sortParam, $filters);
                $serviceTime = round(microtime(true) - $serviceStartTime, 4);
                return $result;
            });

            $totalTime = round(microtime(true) - $startTime, 4);

            if ($responseData !== null && isset($responseData['list'])) {
                return ResponseAdapter::success('获取钥匙卡排行榜成功', $responseData);
            } else {
                Log::warning('钥匙卡排行榜返回数据为空');
                return ResponseAdapter::error('获取钥匙卡排行榜失败：数据为空');
            }
        } catch (\Throwable $e) {
            $totalTime = round(microtime(true) - $startTime, 4);
            Log::error('获取钥匙卡排行榜失败', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                '耗时' => $totalTime . '秒'
            ]);
            return ResponseAdapter::error('获取钥匙卡排行榜失败：' . $e->getMessage());
        }
    }

    /**
     * 获取子弹排行榜数据
     *
     * @param Request $request
     * @return \think\Response
     */
    public function getBulletRanking(Request $request): \think\Response
    {
        try {
            // -------- 参数解析 --------
            $page      = max(1, (int)$request->param('page', 1));
            $pageSize  = max(1, (int)$request->param('page_size', 20));
            $sortParam = $request->param('sort', 'current_price_desc');
            
            // 获取筛选参数
            $grade = $request->param('grade', null);
            $minPrice = $request->param('min_price', null);
            $maxPrice = $request->param('max_price', null);
            $itemType = $request->param('item_type', null);
            
            // 构建筛选参数数组
            $filters = [
                'grade' => $grade !== null ? (int)$grade : null,
                'min_price' => $minPrice !== null ? (float)$minPrice : null,
                'max_price' => $maxPrice !== null ? (float)$maxPrice : null,
                'item_type' => $itemType,
            ];

            // -------- 缓存处理 --------
            $filterHash = md5(json_encode($filters));
            $cacheKey = sprintf('bullet_ranking_v2:%d:%d:%s:%s', $page, $pageSize, $sortParam, $filterHash);
            
            // 使用CacheManager获取缓存数据
            $responseData = $this->cacheManager->remember($cacheKey, CacheManager::TYPE_PRICE_DATA, function() use ($page, $pageSize, $sortParam, $filters) {
                return $this->rankingService->getBulletRankingData($page, $pageSize, $sortParam, $filters);
            });
            
            if ($responseData !== null) {
                return ResponseAdapter::success('获取子弹排行榜成功', $responseData);
            }

            // 如果没有缓存数据，返回空结果（应该不会到这里，因为CacheManager会执行回调）
            return ResponseAdapter::error('获取子弹排行榜失败');
        } catch (\Throwable $e) {
            Log::error('获取子弹排行榜失败: ' . $e->getMessage());
            return ResponseAdapter::error('获取子弹排行榜失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取子弹卡包排行榜数据 - 后端配置方案
     *
     * @param Request $request
     * @return \think\Response
     */
    public function getBulletPackageRanking(Request $request): \think\Response
    {
        try {
            // -------- 参数解析 --------
            $grade = (int)$request->param('grade', 0); // 等级筛选，0表示获取所有等级
            $page = max(1, (int)$request->param('page', 1));
            $pageSize = max(1, (int)$request->param('page_size', 10));

            // -------- 缓存处理 --------
            $cacheKey = sprintf('bullet_package_ranking_v3:%d:%d:%d', $grade, $page, $pageSize);
            
            // 使用CacheManager获取缓存数据
            $responseData = $this->cacheManager->remember($cacheKey, CacheManager::TYPE_PRICE_DATA, function() use ($grade, $page, $pageSize) {
                return $this->rankingService->getBulletPackageRankingData($grade, $page, $pageSize);
            });
            
            return ResponseAdapter::success('获取子弹卡包排行榜成功', $responseData);
        } catch (\Throwable $e) {
            Log::error('获取子弹卡包排行榜失败: ' . $e->getMessage());
            return ResponseAdapter::error('获取子弹卡包排行榜失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取卡包配置
     */
    private function getBulletPackageConfigs()
    {
        return [
            'grade_3' => [
                'name' => '3级弹药卡包',
                'bullets' => [
                    '5.56x45mm M855' => 200,
                    '9x39mm SP5' => 150,
                    '7.62x54R T46M' => 150,
                    '.45 ACP FMJ' => 180,
                    '5.7x28mm L191' => 200,
                    '4.6x30mm Subsonic SX' => 200,
                    '9x19mm AP6.3' => 200,
                    '.50 AE JHP' => 150,
                    '5.8x42mm DVP88' => 180,
                    '7.62x39mm PS' => 150,
                    '7.62x51mm BPZ' => 150,
                    '5.45x39mm PS' => 200,
                    '.357 Magnum JHP' => 150,
                    '12.7x55mm PS12A' => 80
                ]
            ],
            'grade_4' => [
                'name' => '4级弹药卡包',
                'bullets' => [
                    '9x39mm SP6' => 150,
                    '7.62x54R LPS' => 150,
                    '6.8x51mm FMJ' => 150,
                    '7.62x39mm BP' => 150,
                    '5.8x42mm DBP10' => 150,
                    '.45 ACP AP' => 150,
                    '5.56x45mm M855A1' => 150,
                    '7.62x51mm M80' => 150,
                    '4.6x30mm FMJ SX' => 150,
                    '5.7x28mm SS193' => 150,
                    '.357 Magnum FMJ' => 120,
                    '9x19mm PBP' => 150,
                    '.50 AE FMJ' => 120,
                    '5.45x39mm BT' => 150,
                    '12.7x55mm PD12双头弹' => 60,
                    '12.7x55mm PS12' => 60
                ]
            ],
            'grade_5' => [
                'name' => '5级弹药卡包',
                'bullets' => [
                    '5.8x42mm DVC12' => 240,
                    '5.56x45mm M995' => 240,
                    '4.6x30mm AP SX' => 240,
                    '7.62x39mm AP' => 200,
                    '6.8x51mm Hybrid' => 200,
                    '9x39mm BP' => 200,
                    '5.7x28mm SS190' => 240,
                    '5.45x39mm BS' => 240,
                    '7.62x51mm M62' => 150,
                    '7.62x54R BT' => 120
                ]
            ],
            'pass_advanced' => [
                'name' => '通行证高级子弹自选包',
                'bullets' => [
                    '5.8x42mm DBP10' => 50,
                    '9x39mm SP6' => 40,
                    '6.8x51mm FMJ' => 40,
                    '.45 ACP AP' => 45,
                    '5.56x45mm M855A1' => 45,
                    '7.62x54R LPS' => 35,
                    '7.62x39mm BP' => 40,
                    '5.7x28mm SS193' => 50,
                    '9x19mm PBP' => 50,
                    '4.6x30mm FMJ SX' => 45,
                    '7.62x51mm M80' => 40,
                    '12.7x55mm PD12双头弹' => 25,
                    '12.7x55mm PS12' => 30,
                    '5.45x39mm BT' => 45,
                    '12 Gauge独头 AP-20' => 35
                ]
            ],
            'pass_basic' => [
                'name' => '通行证基础子弹自选包',
                'bullets' => [
                    '9x39mm SP5' => 90,
                    '5.56x45mm M855' => 110,
                    '.45 ACP FMJ' => 100,
                    '5.7x28mm L191' => 110,
                    '7.62x54R T46M' => 80,
                    '5.8x42mm DVP88' => 110,
                    '7.62x39mm PS' => 90,
                    '4.6x30mm Subsonic SX' => 100,
                    '9x19mm AP6.3' => 110,
                    '7.62x51mm BPZ' => 90,
                    '5.45x39mm PS' => 100,
                    '12 Gauge 箭形弹' => 70,
                    '12.7x55mm PS12A' => 65
                ]
            ]
        ];
    }

    /**
     * 获取卡包数据
     */
    private function getBulletPackageData($packageId, $page = 1, $pageSize = 10)
    {
        $configs = $this->getBulletPackageConfigs();

        if (!isset($configs[$packageId])) {
            return ['list' => [], 'total' => 0];
        }

        $config = $configs[$packageId];
        $bulletNames = array_keys($config['bullets']);

        // 获取子弹数据
        $items = Db::name('sjz_items')
            ->alias('i')
            ->join('sjz_item_categories c', 'i.category_id = c.id')
            ->field([
                'i.object_id',
                'i.object_name as name',
                'i.pic as image_url',
                'i.grade',
                'c.second_class_cn as bullet_type'
            ])
            ->where('c.primary_class', 'ammo')
            ->whereIn('i.object_name', $bulletNames)
            ->whereNull('i.delete_time')
            ->select()
            ->toArray();

        // 添加价格信息和卡包数量
        $items = $this->addBulletPriceInfo($items);

        // 为每个子弹添加卡包数量和总价值
        foreach ($items as &$item) {
            $bulletName = $item['name'];
            if (isset($config['bullets'][$bulletName])) {
                $item['quantity'] = (int)$config['bullets'][$bulletName];
                $item['total_value'] = (float)($item['current_price'] * $item['quantity']);
            } else {
                $item['quantity'] = 0;
                $item['total_value'] = 0.0;
            }
        }

        // 按总价值排序
        usort($items, function($a, $b) {
            return $b['total_value'] <=> $a['total_value'];
        });

        // 如果需要分页
        if ($page > 0 && $pageSize > 0) {
            $total = count($items);
            $offset = ($page - 1) * $pageSize;
            $pagedItems = array_slice($items, $offset, $pageSize);

            return [
                'list' => $pagedItems,
                'total' => $total,
                'page' => $page,
                'page_size' => $pageSize
            ];
        }

        return [
            'list' => $items,
            'total' => count($items)
        ];
    }

    /**
     * 为子弹数据添加价格信息
     */
    private function addBulletPriceInfo($items)
    {
        if (empty($items)) {
            return $items;
        }

        $objectIds = array_column($items, 'object_id');

        // 获取当前价格
        $currentPrices = Db::name('sjz_latest_prices')
            ->whereIn('object_id', $objectIds)
            ->column('current_price', 'object_id');

        // 为每个子弹添加价格信息
        foreach ($items as &$item) {
            $objectId = $item['object_id'];
            $currentPrice = $currentPrices[$objectId] ?? 0;

            $item['current_price'] = (float)$currentPrice;
        }

        return $items;
    }

    /**
     * 获取子弹价格数据 - 通用接口，供前端配置驱动使用
     *
     * @param Request $request
     * @return \think\Response
     */
    public function getBulletPrices(Request $request): \think\Response
    {
        try {
            // -------- 参数解析 --------
            $objectIds = $request->param('object_ids', ''); // 子弹ID列表，逗号分隔
            $grades = $request->param('grades', ''); // 等级列表，逗号分隔，可选

            // -------- 缓存处理 --------
            $cacheKey = 'bullet_prices:' . md5($objectIds . $grades);
            
            // 使用CacheManager获取缓存数据
            $responseData = $this->cacheManager->remember($cacheKey, CacheManager::TYPE_PRICE_DATA, function() use ($objectIds, $grades) {
                return $this->rankingService->getBulletPricesData($objectIds, $grades);
            });
            
            return ResponseAdapter::success('获取子弹价格成功', $responseData);
        } catch (\Throwable $e) {
            Log::error('获取子弹价格失败: ' . $e->getMessage());
            return ResponseAdapter::error('获取子弹价格失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取指定物品的最新价格
     * @param int $objectId
     * @return float
     */
    private function getLatestPrice(int $objectId): float
    {
        $price = Db::name('sjz_latest_prices')
            ->where('object_id', $objectId)
            ->where('current_price', '>', 0)
            ->value('current_price');

        return $price !== null ? (float)$price : 0.0;
    }

    /**
     * 获取指定物品在若干天前最近一次记录的价格
     * @param int $objectId
     * @param int $days
     * @return float
     */
    private function getPriceBeforeDays(int $objectId, int $days): float
    {
        // 根据天数选择对应的历史价格字段
        $priceField = match($days) {
            1 => 'price_24h_ago',
            7 => 'price_7d_ago', 
            30 => 'price_30d_ago',
            default => 'price_24h_ago'
        };

        $price = Db::name('sjz_latest_prices')
            ->where('object_id', $objectId)
            ->where($priceField, '>', 0)
            ->value($priceField);

        return $price !== null ? (float)$price : 0.0;
    }

}