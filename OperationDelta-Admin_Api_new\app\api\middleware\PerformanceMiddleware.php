<?php
declare(strict_types=1);

namespace app\api\middleware;

use app\api\service\PerformanceMonitor;
use think\facade\Log;
use think\facade\Config;

/**
 * API性能监控中间件
 * 自动为所有API控制器添加性能监控
 */
class PerformanceMiddleware
{
    private static array $monitors = [];
    
    public function handle($request, \Closure $next)
    {
        // 检查是否启用性能监控
        if (!$this->isEnabled()) {
            return $next($request);
        }
        
        // 获取请求信息
        $controller = $this->getControllerName($request);
        $action = $this->getActionName($request);
        $apiName = $controller . '::' . $action;
        
        // 检查是否需要跳过监控
        if ($this->shouldSkipMonitoring($controller, $action)) {
            return $next($request);
        }
        
        // 创建性能监控器
        $monitor = new PerformanceMonitor();
        self::$monitors[$apiName] = $monitor;
        
        // 开始监控
        $this->startMonitoring($monitor, $request, $apiName);
        
        try {
            // 执行请求
            $monitor->startTimer('request_processing');
            $response = $next($request);
            $processingTime = $monitor->endTimer('request_processing');
            
            // 分析响应
            $this->analyzeResponse($monitor, $response, $processingTime);
            
            $monitor->endTimer('middleware_total');
            
            // 生成性能报告
            $this->generateAndLogReport($monitor, $request, $apiName);
            
            return $response;
            
        } catch (\Throwable $e) {
            $this->handleException($monitor, $e, $request, $apiName);
            throw $e;
        } finally {
            // 清理监控器
            unset(self::$monitors[$apiName]);
        }
    }
    
    /**
     * 检查是否启用性能监控
     */
    private function isEnabled(): bool
    {
        return Config::get('performance.enabled', true);
    }
    
    /**
     * 获取控制器名称
     */
    private function getControllerName($request): string
    {
        $controller = $request->controller();
        // 移除命名空间，只保留类名
        if (strpos($controller, '\\') !== false) {
            $controller = substr(strrchr($controller, '\\'), 1);
        }
        return $controller;
    }
    
    /**
     * 获取方法名称
     */
    private function getActionName($request): string
    {
        return $request->action();
    }
    
    /**
     * 检查是否需要跳过监控
     */
    private function shouldSkipMonitoring(string $controller, string $action): bool
    {
        // 检查是否在跳过控制器列表中
        $skipControllers = Config::get('performance.skip_controllers', ['Error', 'Miss']);
        if (in_array($controller, $skipControllers)) {
            return true;
        }

        // 检查是否在监控白名单中
        $monitoredApis = Config::get('performance.monitored_apis', []);
        if (!empty($monitoredApis)) {
            $apiIdentifier = $controller . '::' . $action;
            return !in_array($apiIdentifier, $monitoredApis) && !in_array($action, $monitoredApis);
        }

        // 检查是否在黑名单中
        $skipActions = Config::get('performance.skip_actions', ['ping', 'health', 'status', 'test']);
        return in_array($action, $skipActions);
    }
    
    /**
     * 开始监控
     */
    private function startMonitoring(PerformanceMonitor $monitor, $request, string $apiName): void
    {
        $monitor->startTimer('middleware_total');
        $monitor->recordMetric('request_method', $request->method());
        $monitor->recordMetric('request_url', $request->url(true));
        $monitor->recordMetric('request_ip', $request->ip());
        $monitor->recordMetric('user_agent', $request->header('User-Agent', 'Unknown'));
        
        // 记录请求参数数量
        $params = $request->param();
        $monitor->recordMetric('request_params_count', count($params), 'count');
        
        // 记录请求大小（粗略估算）
        $contentLength = $request->header('Content-Length', '0');
        if ((int)$contentLength > 0) {
            $monitor->recordMetric('request_size', (int)$contentLength, 'bytes');
        }
        
        // 启用SQL查询监控
        if (Config::get('performance.monitor_sql', true)) {
            $this->enableSqlMonitoring($monitor);
        }
        
        // 记录并发请求数
        $this->recordConcurrentRequests($monitor, $apiName);
    }
    
    /**
     * 分析响应
     */
    private function analyzeResponse(PerformanceMonitor $monitor, $response, float $processingTime): void
    {
        // 记录响应状态码
        if (method_exists($response, 'getCode')) {
            $monitor->recordMetric('http_status_code', $response->getCode());  
        }
        
        // 分析响应数据
        $responseData = null;
        if (method_exists($response, 'getData')) {
            $responseData = $response->getData();
        } elseif (method_exists($response, 'getContent')) {
            $content = $response->getContent();
            $responseData = json_decode($content, true);
        }
        
        if (is_array($responseData)) {
            // 记录API返回码
            if (isset($responseData['code'])) {
                $monitor->recordMetric('api_result_code', $responseData['code']);
                $monitor->recordMetric('api_success', $responseData['code'] == 1 ? 'true' : 'false');
            }
            
            // 记录返回数据量
            if (isset($responseData['data']) && is_array($responseData['data'])) {
                if (isset($responseData['data']['list'])) {
                    $listCount = count($responseData['data']['list']);
                    $monitor->recordMetric('response_records', $listCount, 'records');
                    
                    // 计算平均处理时间每条记录
                    if ($listCount > 0 && $processingTime > 0) {
                        $avgTimePerRecord = $processingTime / $listCount;
                        $monitor->recordMetric('avg_time_per_record', round($avgTimePerRecord, 6), 'seconds');
                    }
                }
                
                if (isset($responseData['data']['total'])) {
                    $monitor->recordMetric('total_available_records', $responseData['data']['total'], 'records');
                }
                
                if (isset($responseData['data']['page']) && isset($responseData['data']['page_size'])) {
                    $monitor->recordMetric('page_info', $responseData['data']['page'] . '/' . $responseData['data']['page_size']);
                }
            }
            
            // 估算响应大小
            $responseSize = strlen(json_encode($responseData));
            $monitor->recordMetric('response_size', $responseSize, 'bytes');
        }
    }
    
    /**
     * 生成并记录性能报告
     */
    private function generateAndLogReport(PerformanceMonitor $monitor, $request, string $apiName): void
    {
        // 收集请求参数（敏感信息过滤）
        $params = $this->filterSensitiveParams($request->param());
        $params['method'] = $request->method();
        $params['url'] = $request->url(true);
        
        // 生成性能报告
        $report = $monitor->generateReport($apiName, $params);
        
        // 检查性能告警
        $this->checkPerformanceAlerts($report, $apiName, $request);
        
        // 性能趋势分析
        $this->analyzePerformanceTrends($report, $apiName);
        
        // 记录额外的统计信息
        $this->logAdditionalStats($report, $apiName);
    }
    
    /**
     * 处理异常情况
     */
    private function handleException(PerformanceMonitor $monitor, \Throwable $e, $request, string $apiName): void
    {
        if (isset($monitor)) {
            $monitor->endTimer('request_processing');
            $monitor->endTimer('middleware_total');
            
            // 记录错误信息
            $monitor->recordMetric('error_occurred', true);
            $monitor->recordMetric('error_type', get_class($e));
            $monitor->recordMetric('error_code', $e->getCode());
            
            $params = [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'method' => $request->method(),
                'url' => $request->url(true)
            ];
            
            $monitor->generateReport($apiName . '_error', $params);
        }
    }
    
    /**
     * 过滤敏感参数
     */
    private function filterSensitiveParams(array $params): array
    {
        $sensitiveKeys = ['password', 'token', 'secret', 'key', 'auth', 'authorization'];
        
        foreach ($params as $key => $value) {
            $lowerKey = strtolower($key);
            foreach ($sensitiveKeys as $sensitiveKey) {
                if (strpos($lowerKey, $sensitiveKey) !== false) {
                    $params[$key] = '***';
                    break;
                }
            }
        }
        
        return $params;
    }
    
    /**
     * 记录额外的统计信息
     */
    private function logAdditionalStats(array $report, string $apiName): void
    {
        // 如果启用详细统计，记录更多信息
        if (Config::get('performance.detailed_stats', false)) {
            Log::info('[性能统计] API调用统计', [
                'api' => $apiName,
                'request_id' => $report['request_id'],
                'duration' => $report['total_duration'],
                'memory' => $report['memory_used'],
                'level' => $report['performance_level'],
                'timestamp' => date('Y-m-d H:i:s')
            ]);
        }
    }
    
    /**
     * 检查性能告警
     */
    private function checkPerformanceAlerts(array $report, string $apiName, $request): void
    {
        if (!Config::get('performance.enable_alerts', true)) {
            return;
        }
        
        $alerts = [];
        
        // 检查总耗时
        $totalThreshold = Config::get('performance.slow_query_thresholds.total_request', 2.0);
        if ($report['total_duration'] > $totalThreshold) {
            $alerts[] = "API响应时间过长: {$report['total_duration']}s (阈值: {$totalThreshold}s)";
        }
        
        // 检查内存使用
        $memoryBytes = $this->parseMemoryToBytes($report['memory_used']);
        $memoryThreshold = Config::get('performance.memory_thresholds.warning', 50 * 1024 * 1024);
        if ($memoryBytes > $memoryThreshold) {
            $alerts[] = "内存使用过高: {$report['memory_used']} (阈值: " . $this->formatMemory($memoryThreshold) . ")";
        }
        
        // 检查缓存命中率
        if (isset($report['cache_hit_rate'])) {
            $cacheThreshold = Config::get('performance.alert_config.low_cache_hit_rate_threshold', 70);
            if ($report['cache_hit_rate'] < $cacheThreshold) {
                $alerts[] = "缓存命中率过低: {$report['cache_hit_rate']}% (阈值: {$cacheThreshold}%)";
            }
        }
        
        // 检查数据库查询次数
        if (isset($report['counters']['db_queries'])) {
            $dbQueryThreshold = Config::get('performance.alert_config.max_db_queries', 10);
            if ($report['counters']['db_queries'] > $dbQueryThreshold) {
                $alerts[] = "数据库查询次数过多: {$report['counters']['db_queries']}次 (阈值: {$dbQueryThreshold}次)";
            }
        }
        
        // 检查响应数据量
        if (isset($report['params']) && is_array($report['params'])) {
            $responseSize = 0;
            if (isset($report['params']['data']) && is_array($report['params']['data'])) {
                $responseSize = strlen(json_encode($report['params']['data']));
                $maxResponseSize = Config::get('performance.alert_config.max_response_size', 1024 * 1024); // 1MB
                if ($responseSize > $maxResponseSize) {
                    $alerts[] = "响应数据量过大: " . $this->formatMemory($responseSize) . " (阈值: " . $this->formatMemory($maxResponseSize) . ")";
                }
            }
        }
        
        // 检查请求处理效率（处理时间与数据量的比率）
        if (isset($report['counters']['db_queries']) && $report['counters']['db_queries'] > 0) {
            $timePerQuery = $report['total_duration'] / $report['counters']['db_queries'];
            $slowQueryThreshold = Config::get('performance.alert_config.slow_query_per_time', 0.5);
            if ($timePerQuery > $slowQueryThreshold) {
                $alerts[] = "单次查询平均耗时过长: " . round($timePerQuery, 3) . "s (阈值: {$slowQueryThreshold}s)";
            }
        }
        
        // 检查慢查询数量
        if (isset($report['counters']['slow_queries']) && $report['counters']['slow_queries'] > 0) {
            $maxSlowQueries = Config::get('performance.alert_config.max_slow_queries', 3);
            if ($report['counters']['slow_queries'] > $maxSlowQueries) {
                $alerts[] = "慢查询数量过多: {$report['counters']['slow_queries']}次 (阈值: {$maxSlowQueries}次)";
            }
        }
        
        // 检查复杂查询比例
        if (isset($report['counters']['complex_queries']) && isset($report['counters']['db_queries'])) {
            $complexRatio = ($report['counters']['complex_queries'] / $report['counters']['db_queries']) * 100;
            $maxComplexRatio = Config::get('performance.alert_config.max_complex_query_ratio', 30);
            if ($complexRatio > $maxComplexRatio) {
                $alerts[] = "复杂查询比例过高: " . round($complexRatio, 1) . "% (阈值: {$maxComplexRatio}%)";
            }
        }
        
        // 检查并发请求数
        if (isset($report['concurrent_requests_total'])) {
            $maxConcurrent = Config::get('performance.max_concurrent_requests', 50);
            if ($report['concurrent_requests_total'] > $maxConcurrent * 0.8) {  // 80%预警
                $alerts[] = "并发请求数接近上限: {$report['concurrent_requests_total']} (上限: {$maxConcurrent})";
            }
        }
        
        // 发送告警 - 添加频率控制
        if (!empty($alerts) && $this->shouldSendAlert($apiName, $alerts)) {
            // 收集请求参数（过滤敏感信息）
            $requestParams = $this->filterSensitiveParams($request->param());
            
            // 收集更详细的请求信息
            $detailedInfo = [
                'api' => $apiName,
                'request_id' => $report['request_id'],
                'alerts' => $alerts,
                'performance_level' => $report['performance_level'],
                'duration' => $report['total_duration'],
                'memory' => $report['memory_used'],
                'peak_memory' => $report['peak_memory'],
                'timestamp' => date('Y-m-d H:i:s'),
                // 详细的请求信息
                'request_info' => [
                    'method' => $request->method(),
                    'url' => $request->url(true),
                    'ip' => $request->ip(),
                    'user_agent' => $request->header('User-Agent', 'Unknown'),
                    'content_type' => $request->header('Content-Type', 'Unknown'),
                    'params_count' => count($requestParams),
                    'params' => $requestParams,
                    'request_size' => $request->header('Content-Length', '0') . ' bytes',
                ],
                // 性能时间分解
                'timing_breakdown' => $report['timers'],
                // 各种计数器
                'counters' => $report['counters'],
            ];
            
            // 添加缓存信息（如果存在）
            if (isset($report['cache_hit_rate'])) {
                $detailedInfo['cache_info'] = [
                    'hit_rate' => $report['cache_hit_rate'] . '%',
                    'cache_hits' => $report['counters']['cache_hits'] ?? 0,
                    'cache_misses' => $report['counters']['cache_misses'] ?? 0,
                ];
            }
            
            // 添加数据库查询效率信息
            if (isset($report['db_queries_per_second'])) {
                $detailedInfo['database_info'] = [
                    'queries_per_second' => $report['db_queries_per_second'],
                    'total_queries' => $report['counters']['db_queries'] ?? 0,
                    'slow_queries' => $report['counters']['slow_queries'] ?? 0,
                    'select_queries' => $report['counters']['select_queries'] ?? 0,
                    'insert_queries' => $report['counters']['insert_queries'] ?? 0,
                    'update_queries' => $report['counters']['update_queries'] ?? 0,
                    'delete_queries' => $report['counters']['delete_queries'] ?? 0,
                    'join_queries' => $report['counters']['join_queries'] ?? 0,
                    'complex_queries' => $report['counters']['complex_queries'] ?? 0,
                    'sql_total_time' => $report['metrics']['sql_total_time'] ?? 0,
                ];
            }
            
            // 添加并发请求信息
            if (isset($report['concurrent_requests_total'])) {
                $detailedInfo['concurrent_info'] = [
                    'total_concurrent' => $report['concurrent_requests_total'],
                    'this_api_concurrent' => $report['concurrent_requests_this_api'] ?? 0,
                ];
            }
            
            Log::warning('[性能告警] API性能异常', $detailedInfo);
        }
    }
    
    /**
     * 解析内存字符串为字节数
     */
    private function parseMemoryToBytes(string $memory): int
    {
        $units = ['B' => 1, 'KB' => 1024, 'MB' => 1024*1024, 'GB' => 1024*1024*1024];
        
        foreach ($units as $unit => $multiplier) {
            if (str_ends_with($memory, $unit)) {
                return (int)(floatval(str_replace($unit, '', $memory)) * $multiplier);
            }
        }
        
        return 0;
    }
    
    /**
     * 格式化内存大小
     */
    private function formatMemory(int $bytes): string
    {
        if ($bytes >= 1024 * 1024 * 1024) {
            return round($bytes / (1024 * 1024 * 1024), 2) . 'GB';
        } elseif ($bytes >= 1024 * 1024) {
            return round($bytes / (1024 * 1024), 2) . 'MB';
        } elseif ($bytes >= 1024) {
            return round($bytes / 1024, 2) . 'KB';
        } else {
            return $bytes . 'B';
        }
    }
    
    /**
     * 获取当前监控器实例
     */
    public static function getCurrentMonitor(string $apiName = ''): ?PerformanceMonitor
    {
        if (empty($apiName)) {
            return !empty(self::$monitors) ? current(self::$monitors) : null;
        }
        
        return self::$monitors[$apiName] ?? null;
    }
    
    /**
     * 启用SQL查询监控
     */
    private function enableSqlMonitoring(PerformanceMonitor $monitor): void
    {
        // 记录SQL查询开始时的状态
        $monitor->recordMetric('sql_monitoring_enabled', true);
        
        // 注册数据库查询监听器
        \think\facade\Db::listen(function ($sql, $time, $explain) use ($monitor) {
            // 记录SQL查询
            $monitor->incrementCounter('db_queries');
            $monitor->recordMetric('sql_total_time', $time, 'seconds');
            
            // 检测慢查询
            $slowQueryThreshold = Config::get('performance.slow_sql_threshold', 1.0);
            if ($time > $slowQueryThreshold) {
                $monitor->incrementCounter('slow_queries');
                
                // 记录慢查询详情
                $slowQueryInfo = [
                    'sql' => $this->sanitizeSql($sql),
                    'time' => $time,
                    'explain' => $explain
                ];
                
                $monitor->recordMetric('slow_query_' . time() . '_' . rand(1000, 9999), $slowQueryInfo);
                
                // 立即记录慢查询日志
                Log::warning('[慢查询告警] SQL执行缓慢', [
                    'sql' => $this->sanitizeSql($sql),
                    'execution_time' => $time . 's',
                    'threshold' => $slowQueryThreshold . 's',
                    'explain' => $explain,
                    'timestamp' => date('Y-m-d H:i:s')
                ]);
            }
            
            // 分析SQL类型
            $this->analyzeSqlType($sql, $monitor);
        });
    }
    
    /**
     * 清理SQL语句，去除敏感信息
     */
    private function sanitizeSql(string $sql): string
    {
        // 限制SQL长度
        if (strlen($sql) > 500) {
            $sql = substr($sql, 0, 497) . '...';
        }
        
        // 替换敏感信息（简单示例）
        $sql = preg_replace('/password\s*=\s*[\'"][^\'"]*[\'"]/i', 'password = ***', $sql);
        $sql = preg_replace('/token\s*=\s*[\'"][^\'"]*[\'"]/i', 'token = ***', $sql);
        
        return $sql;
    }
    
    /**
     * 分析SQL类型
     */
    private function analyzeSqlType(string $sql, PerformanceMonitor $monitor): void
    {
        $sql = strtoupper(trim($sql));
        
        if (strpos($sql, 'SELECT') === 0) {
            $monitor->incrementCounter('select_queries');
        } elseif (strpos($sql, 'INSERT') === 0) {
            $monitor->incrementCounter('insert_queries');
        } elseif (strpos($sql, 'UPDATE') === 0) {
            $monitor->incrementCounter('update_queries');
        } elseif (strpos($sql, 'DELETE') === 0) {
            $monitor->incrementCounter('delete_queries');
        }
        
        // 检测复杂查询
        if (strpos($sql, 'JOIN') !== false) {
            $monitor->incrementCounter('join_queries');
        }
        if (strpos($sql, 'SUBQUERY') !== false || strpos($sql, 'EXISTS') !== false) {
            $monitor->incrementCounter('complex_queries');
        }
    }
    
    /**
     * 记录并发请求数
     */
    private function recordConcurrentRequests(PerformanceMonitor $monitor, string $apiName): void
    {
        static $concurrentRequests = [];
        
        // 使用文件锁确保原子操作
        $lockFile = runtime_path() . 'concurrent_requests.lock';
        $fp = fopen($lockFile, 'w+');
        
        if (flock($fp, LOCK_EX)) {
            $concurrentRequests[$apiName] = ($concurrentRequests[$apiName] ?? 0) + 1;
            $totalConcurrent = array_sum($concurrentRequests);
            
            $monitor->recordMetric('concurrent_requests_this_api', $concurrentRequests[$apiName]);
            $monitor->recordMetric('concurrent_requests_total', $totalConcurrent);
            
            // 检查并发数告警
            $maxConcurrent = Config::get('performance.max_concurrent_requests', 50);
            if ($totalConcurrent > $maxConcurrent) {
                Log::warning('[并发告警] 系统并发请求数过高', [
                    'current_concurrent' => $totalConcurrent,
                    'threshold' => $maxConcurrent,
                    'api_breakdown' => $concurrentRequests,
                    'timestamp' => date('Y-m-d H:i:s')
                ]);
            }
            
            flock($fp, LOCK_UN);
        }
        
        fclose($fp);
        
        // 注册请求结束时的清理回调
        register_shutdown_function(function() use ($apiName) {
            static $concurrentRequests = [];
            $lockFile = runtime_path() . 'concurrent_requests.lock';
            $fp = fopen($lockFile, 'w+');
            
            if (flock($fp, LOCK_EX)) {
                if (isset($concurrentRequests[$apiName]) && $concurrentRequests[$apiName] > 0) {
                    $concurrentRequests[$apiName]--;
                }
                flock($fp, LOCK_UN);
            }
            
            fclose($fp);
        });
    }
    
    /**
     * 性能趋势分析
     */
    private function analyzePerformanceTrends(array $report, string $apiName): void
    {
        if (!Config::get('performance.enable_trend_analysis', false)) {
            return;
        }
        
        $trendsFile = runtime_path() . 'performance_trends.json';
        $trends = [];
        
        if (file_exists($trendsFile)) {
            $trends = json_decode(file_get_contents($trendsFile), true) ?: [];
        }
        
        // 保存最近50次调用的性能数据
        if (!isset($trends[$apiName])) {
            $trends[$apiName] = [];
        }
        
        $trends[$apiName][] = [
            'timestamp' => time(),
            'duration' => $report['total_duration'],
            'memory' => $this->parseMemoryToBytes($report['memory_used']),
            'db_queries' => $report['counters']['db_queries'] ?? 0
        ];
        
        // 只保留最近50次记录
        if (count($trends[$apiName]) > 50) {
            $trends[$apiName] = array_slice($trends[$apiName], -50);
        }
        
        // 分析趋势
        if (count($trends[$apiName]) >= 10) {
            $this->checkPerformanceRegression($trends[$apiName], $apiName);
        }
        
        file_put_contents($trendsFile, json_encode($trends));
    }
    
    /**
     * 检查性能回归
     */
    private function checkPerformanceRegression(array $history, string $apiName): void
    {
        $recent = array_slice($history, -5); // 最近5次
        $previous = array_slice($history, -15, 10); // 之前10次
        
        $recentAvg = array_sum(array_column($recent, 'duration')) / count($recent);
        $previousAvg = array_sum(array_column($previous, 'duration')) / count($previous);
        
        // 如果最近的平均响应时间比之前慢了50%以上
        if ($recentAvg > $previousAvg * 1.5) {
            Log::warning('[性能回归告警] API性能明显下降', [
                'api' => $apiName,
                'recent_avg_duration' => round($recentAvg, 3) . 's',
                'previous_avg_duration' => round($previousAvg, 3) . 's',
                'regression_percentage' => round((($recentAvg - $previousAvg) / $previousAvg) * 100, 1) . '%',
                'timestamp' => date('Y-m-d H:i:s')
            ]);
        }
    }
    
    /**
     * 检查是否应该发送告警（频率控制）
     */
    private function shouldSendAlert(string $apiName, array $alerts): bool
    {
        $alertKey = md5($apiName . implode('|', $alerts));
        $alertFile = runtime_path() . 'alert_history.json';
        $alertHistory = [];
        
        if (file_exists($alertFile)) {
            $alertHistory = json_decode(file_get_contents($alertFile), true) ?: [];
        }
        
        $now = time();
        $cooldownPeriod = Config::get('performance.alert_cooldown', 300); // 5分钟冷却期
        
        // 检查是否在冷却期内
        if (isset($alertHistory[$alertKey])) {
            $lastAlertTime = $alertHistory[$alertKey]['last_time'];
            if (($now - $lastAlertTime) < $cooldownPeriod) {
                // 增加跳过计数
                $alertHistory[$alertKey]['skipped_count'] = ($alertHistory[$alertKey]['skipped_count'] ?? 0) + 1;
                file_put_contents($alertFile, json_encode($alertHistory));
                return false;
            }
        }
        
        // 记录本次告警
        $alertHistory[$alertKey] = [
            'api' => $apiName,
            'first_time' => $alertHistory[$alertKey]['first_time'] ?? $now,
            'last_time' => $now,
            'count' => ($alertHistory[$alertKey]['count'] ?? 0) + 1,
            'skipped_count' => 0,
        ];
        
        // 清理过期的告警记录（保留最近24小时）
        $expireTime = $now - 86400; // 24小时
        foreach ($alertHistory as $key => $data) {
            if ($data['last_time'] < $expireTime) {
                unset($alertHistory[$key]);
            }
        }
        
        file_put_contents($alertFile, json_encode($alertHistory));
        return true;
    }
}