<?php
declare(strict_types=1);

namespace app\api\service;

use think\facade\Log;
use think\facade\Config;

/**
 * API性能监控工具类 - 优化版
 * 用于统计API各个环节的性能指标，避免阻塞
 */
class PerformanceMonitor
{
    private array $timers = [];
    private array $counters = [];
    private array $memory = [];
    private string $requestId;
    private float $startTime;
    private int $startMemory;
    private static array $logBuffer = [];
    private bool $enableLogging;
    
    public function __construct()
    {
        $this->requestId = uniqid('perf_', true);
        $this->startTime = microtime(true);
        $this->startMemory = memory_get_usage(true);
        $this->enableLogging = $this->shouldEnableLogging();
        
        // 只在高优先级日志级别下记录开始信息
        if ($this->enableLogging && $this->shouldLog('info')) {
            $this->bufferLog('info', '[性能监控] 开始监控', [
                'request_id' => $this->requestId,
                'start_time' => date('H:i:s', (int)$this->startTime),
                'start_memory' => $this->formatMemory($this->startMemory)
            ]);
        }
    }
    
    /**
     * 析构函数，确保日志缓冲区被刷新
     */
    public function __destruct()
    {
        $this->flushLogBuffer();
    }
    
    /**
     * 检查是否应该启用日志记录
     */
    private function shouldEnableLogging(): bool
    {
        // 采样率控制
        $samplingRate = Config::get('performance.sampling_rate', 100);
        return mt_rand(1, 100) <= $samplingRate;
    }
    
    /**
     * 检查是否应该记录指定级别的日志
     */
    private function shouldLog(string $level): bool
    {
        $configLevel = Config::get('performance.log_level', 'info');
        $levels = ['debug' => 0, 'info' => 1, 'warning' => 2, 'error' => 3];
        
        return ($levels[$level] ?? 1) >= ($levels[$configLevel] ?? 1);
    }
    
    /**
     * 缓存日志，避免频繁写入
     */
    private function bufferLog(string $level, string $message, array $context = []): void
    {
        if (!$this->enableLogging) {
            return;
        }
        
        self::$logBuffer[] = [
            'level' => $level,
            'message' => $message,
            'context' => $context,
            'time' => microtime(true)
        ];
        
        // 当缓冲区达到一定大小时，批量写入
        $batchSize = Config::get('performance.log_batch_size', 10);
        if (count(self::$logBuffer) >= $batchSize) {
            $this->flushLogBuffer();
        }
    }
    
    /**
     * 刷新日志缓冲区
     */
    private function flushLogBuffer(): void
    {
        if (empty(self::$logBuffer)) {
            return;
        }
        
        // 异步写入日志
        if (Config::get('performance.async_logging', true)) {
            $this->asyncWriteLogs();
        } else {
            $this->syncWriteLogs();
        }
        
        self::$logBuffer = [];
    }
    
    /**
     * 异步写入日志
     */
    private function asyncWriteLogs(): void
    {
        // 简化的异步实现，避免阻塞主请求
        try {
            foreach (self::$logBuffer as $logEntry) {
                $method = $logEntry['level'];
                if (method_exists(Log::class, $method)) {
                    Log::$method($logEntry['message'], $logEntry['context']);
                }
            }
        } catch (\Throwable $e) {
            // 日志写入失败不应该影响主业务
        }
    }
    
    /**
     * 同步写入日志
     */
    private function syncWriteLogs(): void
    {
        try {
            foreach (self::$logBuffer as $logEntry) {
                $method = $logEntry['level'];
                if (method_exists(Log::class, $method)) {
                    Log::$method($logEntry['message'], $logEntry['context']);
                }
            }
        } catch (\Throwable $e) {
            // 日志写入失败不应该影响主业务
        }
    }
    
    /**
     * 开始计时
     */
    public function startTimer(string $name): void
    {
        $this->timers[$name] = [
            'start' => microtime(true),
            'end' => null,
            'duration' => null
        ];
        
        $this->memory[$name . '_start'] = memory_get_usage(true);
        
        // 只在debug级别记录计时器开始
        if ($this->shouldLog('debug')) {
            $this->bufferLog('debug', '[性能监控] 计时器开始', [
                'request_id' => $this->requestId,
                'timer' => $name
            ]);
        }
    }
    
    /**
     * 结束计时
     */
    public function endTimer(string $name): float
    {
        if (!isset($this->timers[$name])) {
            if ($this->shouldLog('warning')) {
                $this->bufferLog('warning', '[性能监控] 计时器未找到', ['timer' => $name]);
            }
            return 0.0;
        }
        
        $this->timers[$name]['end'] = microtime(true);
        $duration = round($this->timers[$name]['end'] - $this->timers[$name]['start'], 4);
        $this->timers[$name]['duration'] = $duration;
        
        $this->memory[$name . '_end'] = memory_get_usage(true);
        $memoryUsed = $this->memory[$name . '_end'] - ($this->memory[$name . '_start'] ?? 0);
        
        // 只记录超过阈值的计时器或重要操作
        $threshold = Config::get('performance.slow_query_thresholds.' . $name, 1.0);
        if ($duration > $threshold || $this->shouldLog('info')) {
            $this->bufferLog('info', '[性能监控] 计时器完成', [
                'request_id' => $this->requestId,
                'timer' => $name,
                'duration' => $duration . 's',
                'memory_used' => $this->formatMemory($memoryUsed),
                'is_slow' => $duration > $threshold
            ]);
        }
        
        return $duration;
    }
    
    /**
     * 增加计数器
     */
    public function increment(string $name, int $count = 1): void
    {
        if (!isset($this->counters[$name])) {
            $this->counters[$name] = 0;
        }
        $this->counters[$name] += $count;
        
        // 只在debug级别且启用详细日志时记录
        if ($this->shouldLog('debug') && Config::get('performance.detailed_stats', false)) {
            $this->bufferLog('debug', '[性能监控] 计数器更新', [
                'request_id' => $this->requestId,
                'counter' => $name,
                'count' => $this->counters[$name]
            ]);
        }
    }
    
    /**
     * 增加计数器（别名方法，兼容旧版本调用）
     */
    public function incrementCounter(string $name, int $count = 1): void
    {
        $this->increment($name, $count);
    }
    
    /**
     * 记录数据库查询性能
     */
    public function recordDbQuery(string $queryType, float $duration, int $recordCount = 0, string $sql = ''): void
    {
        $this->increment('db_queries');
        
        $logData = [
            'request_id' => $this->requestId,
            'query_type' => $queryType,
            'duration' => $duration . 's',
            'record_count' => $recordCount,
            'queries_per_second' => $duration > 0 ? round($recordCount / $duration, 2) : 0
        ];
        
        if (!empty($sql) && strlen($sql) <= 200) {
            $logData['sql'] = $sql;
        } elseif (!empty($sql)) {
            $logData['sql'] = substr($sql, 0, 197) . '...';
        }
        
        Log::info('[性能监控] 数据库查询', $logData);
    }
    
    /**
     * 记录缓存操作
     */
    public function recordCacheOperation(string $operation, string $key, bool $hit = false, float $duration = 0): void
    {
        $counterName = 'cache_' . $operation;
        $this->increment($counterName);
        
        if ($hit) {
            $this->increment('cache_hits');
        } else {
            $this->increment('cache_misses');
        }
        
        Log::info('[性能监控] 缓存操作', [
            'request_id' => $this->requestId, 
            'operation' => $operation,
            'key' => $key,
            'hit' => $hit ? 'HIT' : 'MISS',
            'duration' => $duration . 's'
        ]);
    }
    
    /**
     * 记录业务指标
     */
    public function recordMetric(string $name, $value, string $unit = ''): void
    {
        // 只在debug级别且启用详细日志时记录指标
        if ($this->shouldLog('debug') && Config::get('performance.detailed_stats', false)) {
            $this->bufferLog('debug', '[性能监控] 业务指标', [
                'request_id' => $this->requestId,
                'metric' => $name,
                'value' => $value,
                'unit' => $unit
            ]);
        }
    }
    
    /**
     * 生成最终性能报告
     */
    public function generateReport(string $apiName, array $params = []): array
    {
        $endTime = microtime(true);
        $endMemory = memory_get_usage(true);
        $totalDuration = round($endTime - $this->startTime, 4);
        $totalMemoryUsed = $endMemory - $this->startMemory;
        $peakMemory = memory_get_peak_usage(true);
        
        $report = [
            'api_name' => $apiName,
            'request_id' => $this->requestId,
            'total_duration' => $totalDuration,
            'memory_used' => $this->formatMemory($totalMemoryUsed),
            'peak_memory' => $this->formatMemory($peakMemory),
            'timers' => [],
            'counters' => $this->counters,
            'params' => $params
        ];
        
        // 整理计时器数据
        foreach ($this->timers as $name => $timer) {
            if ($timer['duration'] !== null) {
                $report['timers'][$name] = [
                    'duration' => $timer['duration'],
                    'percentage' => $totalDuration > 0 ? round(($timer['duration'] / $totalDuration) * 100, 2) : 0
                ];
            }
        }
        
        // 计算缓存命中率
        if (isset($this->counters['cache_hits']) && isset($this->counters['cache_misses'])) {
            $totalCacheOps = $this->counters['cache_hits'] + $this->counters['cache_misses'];
            $report['cache_hit_rate'] = $totalCacheOps > 0 ? round(($this->counters['cache_hits'] / $totalCacheOps) * 100, 2) : 0;
        }
        
        // 计算数据库查询效率 
        if (isset($this->counters['db_queries'])) {
            $report['db_queries_per_second'] = $totalDuration > 0 ? round($this->counters['db_queries'] / $totalDuration, 2) : 0;
        }
        
        // 性能等级评估
        $report['performance_level'] = $this->assessPerformance($totalDuration, $totalMemoryUsed);
        
        // 只记录重要的性能报告（超过阈值或警告级别）
        $isSlowRequest = $totalDuration > Config::get('performance.slow_query_thresholds.total_request', 2.0);
        if ($isSlowRequest || $this->shouldLog('warning')) {
            $this->bufferLog('info', '[性能监控] 最终报告', array_merge($report, [
                'is_slow_request' => $isSlowRequest
            ]));
        }
        
        // 确保日志缓冲区被刷新
        $this->flushLogBuffer();
        
        return $report;
    }
    
    /**
     * 性能等级评估
     */
    private function assessPerformance(float $duration, int $memoryUsed): string
    {
        if ($duration < 0.1 && $memoryUsed < 5 * 1024 * 1024) { // 100ms, 5MB
            return '优秀';
        } elseif ($duration < 0.5 && $memoryUsed < 20 * 1024 * 1024) { // 500ms, 20MB
            return '良好';
        } elseif ($duration < 1.0 && $memoryUsed < 50 * 1024 * 1024) { // 1s, 50MB
            return '一般';
        } elseif ($duration < 3.0 && $memoryUsed < 100 * 1024 * 1024) { // 3s, 100MB
            return '较差';
        } else {
            return '差';
        }
    }
    
    /**
     * 格式化内存大小
     */
    private function formatMemory(int $bytes): string
    {
        if ($bytes >= 1024 * 1024 * 1024) {
            return round($bytes / (1024 * 1024 * 1024), 2) . 'GB';
        } elseif ($bytes >= 1024 * 1024) {
            return round($bytes / (1024 * 1024), 2) . 'MB';
        } elseif ($bytes >= 1024) {
            return round($bytes / 1024, 2) . 'KB';
        } else {
            return $bytes . 'B';
        }
    }
    
    /**
     * 记录慢查询警告
     */
    public function checkSlowQuery(string $queryType, float $duration, float $threshold = 1.0): void
    {
        if ($duration > $threshold) {
            Log::warning('[性能监控] 慢查询警告', [
                'request_id' => $this->requestId,
                'query_type' => $queryType,
                'duration' => $duration . 's',
                'threshold' => $threshold . 's',
                'slow_ratio' => round($duration / $threshold, 2)
            ]);
        }
    }
    
    /**
     * 记录内存使用警告
     */
    public function checkMemoryUsage(string $operation, int $threshold = 50 * 1024 * 1024): void
    {
        $currentMemory = memory_get_usage(true);
        if ($currentMemory > $threshold) {
            Log::warning('[性能监控] 内存使用警告', [
                'request_id' => $this->requestId,
                'operation' => $operation,
                'current_memory' => $this->formatMemory($currentMemory),
                'threshold' => $this->formatMemory($threshold),
                'peak_memory' => $this->formatMemory(memory_get_peak_usage(true))
            ]);
        }
    }
}