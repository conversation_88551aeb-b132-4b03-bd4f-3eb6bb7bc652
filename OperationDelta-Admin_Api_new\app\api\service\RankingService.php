<?php
declare(strict_types=1);

namespace app\api\service;

use think\facade\Db;
use think\facade\Log;

/**
 * 排行榜数据服务类
 * 负责处理各种排行榜的数据获取逻辑
 */
class RankingService
{
    /**
     * 获取钥匙卡排行榜数据 - 优化版本
     */
    public function getKeycardRankingData(int $page, int $pageSize, string $sortParam, array $filters = []): array
    {
        try {
            // -------- 动态获取钥匙卡分类ID --------
            $keyCategoryIds = Db::name('sjz_item_categories')
                ->where('second_class', 'key')
                ->column('id');

            if (empty($keyCategoryIds)) {
                return [
                    'list'      => [],
                    'total'     => 0,
                    'page'      => $page,
                    'page_size' => $pageSize
                ];
            }

            // -------- 获取全部钥匙卡基础数据 --------
            $query = Db::name('sjz_items')
                ->alias('i')
                ->field(['i.object_id', 'i.object_name as name', 'i.pic as image_url', 'i.grade'])
                ->whereIn('i.category_id', $keyCategoryIds)
                ->whereNull('i.delete_time');
            
            // 应用等级筛选 (1-6级)
            if (isset($filters['grade']) && $filters['grade'] !== null && $filters['grade'] > 0) {
                $query->where('i.grade', $filters['grade']);
            }
            
            $items = $query->select()->toArray();

            if (empty($items)) {
                return [
                    'list'      => [],
                    'total'     => 0,
                    'page'      => $page,
                    'page_size' => $pageSize
                ];
            }

            $total = count($items);
            $objectIds = array_column($items, 'object_id');

            // -------- 批量获取扩展价格数据 --------
            $priceData = $this->getBatchExtendedPriceData($objectIds);

            // -------- 合并价格数据 --------
            foreach ($items as &$item) {
                $objectId = (int)$item['object_id'];
                $prices = $priceData[$objectId] ?? $this->getDefaultPriceData();

                // 基础价格数据
                $item['current_price'] = (float)number_format($prices['current_price'], 2, '.', '');
                
                // 日涨幅数据
                $item['day_price'] = (float)number_format($prices['day_price'], 2, '.', '');
                $item['day_change'] = (float)number_format($prices['day_change'], 2, '.', '');
                $item['day_change_pct'] = (float)number_format($prices['day_change_pct'], 2, '.', '');

                // 周涨幅数据
                $item['week_price'] = (float)number_format($prices['week_price'], 2, '.', '');
                $item['week_change'] = (float)number_format($prices['week_change'], 2, '.', '');
                $item['week_change_pct'] = (float)number_format($prices['week_change_pct'], 2, '.', '');

                // 月涨幅数据
                $item['month_price'] = (float)number_format($prices['month_price'], 2, '.', '');
                $item['month_change'] = (float)number_format($prices['month_change'], 2, '.', '');
                $item['month_change_pct'] = (float)number_format($prices['month_change_pct'], 2, '.', '');
                
                // 扩展数据：24小时价格区间
                $item['highest_price_24h'] = (float)number_format($prices['highest_price_24h'], 2, '.', '');
                $item['lowest_price_24h'] = (float)number_format($prices['lowest_price_24h'], 2, '.', '');
                $item['avg_price_24h'] = (float)number_format($prices['avg_price_24h'], 2, '.', '');
                
                // 扩展数据：7天价格区间
                $item['highest_price_7d'] = (float)number_format($prices['highest_price_7d'], 2, '.', '');
                $item['lowest_price_7d'] = (float)number_format($prices['lowest_price_7d'], 2, '.', '');
                
                // 扩展数据：价格趋势和波动率
                $item['price_trend'] = $prices['price_trend'] ?? '平稳';
                $item['volatility_24h'] = (float)number_format($prices['volatility_24h'], 4, '.', '');
                
                // 扩展数据：价格区间百分比
                if ($item['current_price'] > 0) {
                    // 当前价格相对于24小时最高价的百分比
                    $item['price_vs_24h_high_pct'] = $item['highest_price_24h'] > 0 ? 
                        (float)number_format(($item['current_price'] / $item['highest_price_24h']) * 100, 2, '.', '') : 100.00;
                    
                    // 当前价格相对于24小时最低价的百分比
                    $item['price_vs_24h_low_pct'] = $item['lowest_price_24h'] > 0 ? 
                        (float)number_format(($item['current_price'] / $item['lowest_price_24h']) * 100, 2, '.', '') : 100.00;
                }
                
                // 添加扩展时间戳数据
                $item['highest_price_24h_time'] = $prices['highest_price_24h_time'];
                $item['lowest_price_24h_time'] = $prices['lowest_price_24h_time'];
            }
            unset($item);

            // -------- 应用价格筛选 --------
            if (isset($filters['min_price']) && $filters['min_price'] !== null && $filters['min_price'] > 0) {
                $items = array_filter($items, function($item) use ($filters) {
                    return $item['current_price'] >= $filters['min_price'];
                });
            }
            
            if (isset($filters['max_price']) && $filters['max_price'] !== null && $filters['max_price'] > 0) {
                $items = array_filter($items, function($item) use ($filters) {
                    return $item['current_price'] <= $filters['max_price'];
                });
            }
            
            // 重新计算总数（因为价格筛选可能减少了结果）
            $total = count($items);

            // -------- 排序 & 分页 --------
            usort($items, function ($a, $b) {
                return $b['current_price'] <=> $a['current_price'];
            });

            $list = array_slice($items, ($page - 1) * $pageSize, $pageSize);

            return [
                'list'      => $list,
                'total'     => $total,
                'page'      => $page,
                'page_size' => $pageSize
            ];
        } catch (\Throwable $e) {
            Log::error('获取钥匙卡排行榜数据失败', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'page' => $page,
                'page_size' => $pageSize
            ]);
            return [
                'list'      => [],
                'total'     => 0,
                'page'      => $page,
                'page_size' => $pageSize
            ];
        }
    }

    /**
     * 获取子弹排行榜数据
     */
    public function getBulletRankingData(int $page, int $pageSize, string $sortParam, array $filters = []): array
    {
        // -------- 动态获取子弹分类ID --------
        $bulletCategoryIds = Db::name('sjz_item_categories')
            ->where('primary_class', 'ammo')
            ->column('id');

        if (empty($bulletCategoryIds)) {
            // 若未找到分类，直接返回空列表
            return [
                'list'      => [],
                'total'     => 0,
                'page'      => $page,
                'page_size' => $pageSize
            ];
        }

        // -------- 获取全部子弹基础数据 --------
        $query = Db::name('sjz_items')
            ->alias('i')
            ->join('sjz_item_categories c', 'i.category_id = c.id')
            ->field([
                'i.object_id',
                'i.object_name as name',
                'i.pic as image_url',
                'i.grade',
                'c.second_class_cn as bullet_type'
            ])
            ->whereIn('i.category_id', $bulletCategoryIds)
            ->whereNull('i.delete_time');
            
        // 应用等级筛选 (1-6级)
        if (isset($filters['grade']) && $filters['grade'] !== null && $filters['grade'] > 0) {
            $query->where('i.grade', $filters['grade']);
        }
        
        // 应用子弹类型筛选
        if (isset($filters['item_type']) && !empty($filters['item_type'])) {
            $query->where(function($q) use ($filters) {
                $q->where('c.primary_class', $filters['item_type'])
                  ->whereOr('c.second_class', $filters['item_type'])
                  ->whereOr('c.second_class_cn', $filters['item_type']);
            });
        }
        
        $items = $query->select()->toArray();

        $total = count($items);

        if ($total === 0) {
            return [
                'list'      => [],
                'total'     => 0,
                'page'      => $page,
                'page_size' => $pageSize
            ];
        }

        // -------- 批量获取扩展价格数据 --------
        $objectIds = array_column($items, 'object_id');
        $priceData = $this->getBatchExtendedPriceData($objectIds);

        // -------- 合并扩展数据 --------
        foreach ($items as &$item) {
            $objectId = $item['object_id'];
            $prices = $priceData[$objectId] ?? $this->getDefaultPriceData();
            
            // 合并所有价格数据，包括扩展字段
            $item = array_merge($item, [
                // 基础价格数据
                'current_price' => (float)number_format($prices['current_price'], 2, '.', ''),
                'day_price' => (float)number_format($prices['day_price'], 2, '.', ''),
                'day_change' => (float)number_format($prices['day_change'], 2, '.', ''),
                'day_change_pct' => (float)number_format($prices['day_change_pct'], 2, '.', ''),
                'week_price' => (float)number_format($prices['week_price'], 2, '.', ''),
                'week_change' => (float)number_format($prices['week_change'], 2, '.', ''),
                'week_change_pct' => (float)number_format($prices['week_change_pct'], 2, '.', ''),
                'month_price' => (float)number_format($prices['month_price'], 2, '.', ''),
                'month_change' => (float)number_format($prices['month_change'], 2, '.', ''),
                'month_change_pct' => (float)number_format($prices['month_change_pct'], 2, '.', ''),
                
                // 扩展数据：价格区间
                'highest_price_24h' => (float)number_format($prices['highest_price_24h'], 2, '.', ''),
                'lowest_price_24h' => (float)number_format($prices['lowest_price_24h'], 2, '.', ''),
                'highest_price_7d' => (float)number_format($prices['highest_price_7d'], 2, '.', ''),
                'lowest_price_7d' => (float)number_format($prices['lowest_price_7d'], 2, '.', ''),
                'avg_price_24h' => (float)number_format($prices['avg_price_24h'], 2, '.', ''),
                
                // 扩展数据：趋势和波动率
                'price_trend' => $prices['price_trend'] ?? '平稳',
                'volatility_24h' => (float)number_format($prices['volatility_24h'], 4, '.', ''),
                
                // 扩展数据：价格相对位置
                'price_vs_24h_high_pct' => $prices['highest_price_24h'] > 0 ? 
                    (float)number_format(($prices['current_price'] / $prices['highest_price_24h']) * 100, 2, '.', '') : 100.00,
                'price_vs_24h_low_pct' => $prices['lowest_price_24h'] > 0 ? 
                    (float)number_format(($prices['current_price'] / $prices['lowest_price_24h']) * 100, 2, '.', '') : 100.00,
                
                // 扩展数据：时间戳
                'highest_price_24h_time' => $prices['highest_price_24h_time'],
                'lowest_price_24h_time' => $prices['lowest_price_24h_time'],
            ]);
        }

        // -------- 应用价格筛选 --------
        if (isset($filters['min_price']) && $filters['min_price'] !== null && $filters['min_price'] > 0) {
            $items = array_filter($items, function($item) use ($filters) {
                return $item['current_price'] >= $filters['min_price'];
            });
        }
        
        if (isset($filters['max_price']) && $filters['max_price'] !== null && $filters['max_price'] > 0) {
            $items = array_filter($items, function($item) use ($filters) {
                return $item['current_price'] <= $filters['max_price'];
            });
        }
        
        // 重新计算总数（因为价格筛选可能减少了结果）
        $total = count($items);

        // 按当前价格降序排序
        usort($items, function($a, $b) {
            return $b['current_price'] <=> $a['current_price'];
        });

        // -------- 分页处理 --------
        $offset = ($page - 1) * $pageSize;
        $pagedItems = array_slice($items, $offset, $pageSize);

        return [
            'list'      => $pagedItems,
            'total'     => $total,
            'page'      => $page,
            'page_size' => $pageSize
        ];
    }

    /**
     * 获取子弹卡包排行榜数据
     */
    public function getBulletPackageRankingData(int $grade, int $page, int $pageSize): array
    {
        if ($grade > 0) {
            // -------- 获取指定卡包的数据 --------
            return $this->getBulletPackageData($grade, $page, $pageSize);
        } else {
            // -------- 获取所有卡包的数据（不分页，返回完整数据） --------
            return [
                'grade_3' => $this->getBulletPackageData('grade_3', 0, 0),
                'grade_4' => $this->getBulletPackageData('grade_4', 0, 0),
                'grade_5' => $this->getBulletPackageData('grade_5', 0, 0),
                'pass_basic' => $this->getBulletPackageData('pass_basic', 0, 0),
                'pass_advanced' => $this->getBulletPackageData('pass_advanced', 0, 0)
            ];
        }
    }

    /**
     * 获取子弹价格数据
     */
    public function getBulletPricesData(string $objectIds, string $grades): array
    {
        // -------- 构建查询条件 --------
        $query = Db::name('sjz_items')
            ->alias('i')
            ->join('sjz_item_categories c', 'i.category_id = c.id')
            ->field([
                'i.object_id',
                'i.object_name as name',
                'i.pic as image_url',
                'i.grade',
                'c.second_class_cn as bullet_type'
            ])
            ->where('c.primary_class', 'ammo')
            ->whereNull('i.delete_time');

        // 如果指定了object_ids，则只查询指定的子弹
        if (!empty($objectIds)) {
            $objectIdArray = array_filter(array_map('intval', explode(',', $objectIds)));
            if (!empty($objectIdArray)) {
                $query->whereIn('i.object_id', $objectIdArray);
            }
        }

        // 如果指定了grades，则只查询指定等级的子弹
        if (!empty($grades)) {
            $gradeArray = array_filter(array_map('intval', explode(',', $grades)));
            if (!empty($gradeArray)) {
                $query->whereIn('i.grade', $gradeArray);
            }
        }

        $items = $query->select()->toArray();

        // 添加价格信息
        $items = $this->addBulletPriceInfo($items);

        return [
            'list' => $items,
            'total' => count($items)
        ];
    }

    /**
     * 批量获取扩展价格数据 - 包含所有价格维度
     */
    private function getBatchExtendedPriceData(array $objectIds): array
    {
        if (empty($objectIds)) {
            return [];
        }

        try {
            // 直接从扩展价格表获取所有字段
            $priceRecords = Db::name('sjz_latest_prices')
                ->whereIn('object_id', $objectIds)
                ->field([
                    'object_id',
                    'current_price',
                    'price_24h_ago',
                    'price_7d_ago', 
                    'price_30d_ago',
                    'price_change_24h',
                    'price_change_24h_percent',
                    'price_change_7d',
                    'price_change_7d_percent',
                    'highest_price_24h',
                    'lowest_price_24h',
                    'highest_price_7d',
                    'lowest_price_7d',
                    'avg_price_24h',
                    'price_trend',
                    'volatility_24h'
                ])
                ->select()
                ->toArray();

            // 批量获取24小时价格极值的时间戳
            $priceTimeStamps = $this->getBatchPriceTimeStamps($objectIds);

            $result = [];
            foreach ($priceRecords as $record) {
                $objectId = $record['object_id'];
                $currentPrice = (float)($record['current_price'] ?? 0);
                $dayPrice = (float)($record['price_24h_ago'] ?? 0);
                $weekPrice = (float)($record['price_7d_ago'] ?? 0);
                $monthPrice = (float)($record['price_30d_ago'] ?? 0);

                // 使用预计算的价格变化，如果没有则自己计算
                $dayChange = $record['price_change_24h'] ?? ($currentPrice - $dayPrice);
                // 注意：数据库中的百分比可能已经是百分比形式，不需要再乘以100
                $dayChangePct = isset($record['price_change_24h_percent']) ? 
                    (float)$record['price_change_24h_percent'] :
                    ($dayPrice > 0 ? (($currentPrice - $dayPrice) / $dayPrice) * 100 : 0);
                
                $weekChange = $record['price_change_7d'] ?? ($currentPrice - $weekPrice);
                $weekChangePct = isset($record['price_change_7d_percent']) ? 
                    (float)$record['price_change_7d_percent'] :
                    ($weekPrice > 0 ? (($currentPrice - $weekPrice) / $weekPrice) * 100 : 0);

                // 获取该物品的时间戳数据
                $timeStamps = $priceTimeStamps[$objectId] ?? [
                    'highest_price_time' => null,
                    'lowest_price_time' => null
                ];

                $result[$objectId] = [
                    'current_price' => $currentPrice,
                    'day_price' => $dayPrice,
                    'day_change' => (float)$dayChange,
                    'day_change_pct' => (float)$dayChangePct,
                    'week_price' => $weekPrice,
                    'week_change' => (float)$weekChange,
                    'week_change_pct' => (float)$weekChangePct,
                    'month_price' => $monthPrice,
                    'month_change' => $currentPrice - $monthPrice,
                    'month_change_pct' => $monthPrice > 0 ? (($currentPrice - $monthPrice) / $monthPrice) * 100 : 0,
                    // 扩展数据
                    'highest_price_24h' => (float)($record['highest_price_24h'] ?? $currentPrice),
                    'lowest_price_24h' => (float)($record['lowest_price_24h'] ?? $currentPrice),
                    'highest_price_7d' => (float)($record['highest_price_7d'] ?? $currentPrice),
                    'lowest_price_7d' => (float)($record['lowest_price_7d'] ?? $currentPrice),
                    'avg_price_24h' => (float)($record['avg_price_24h'] ?? $currentPrice),
                    'price_trend' => $record['price_trend'] ?? '平稳',
                    'volatility_24h' => (float)($record['volatility_24h'] ?? 0),
                    // 时间戳数据
                    'highest_price_24h_time' => $timeStamps['highest_price_time'],
                    'lowest_price_24h_time' => $timeStamps['lowest_price_time'],
                ];
            }

            // 为没有价格数据的物品填充默认数据
            foreach ($objectIds as $objectId) {
                if (!isset($result[$objectId])) {
                    $result[$objectId] = $this->getDefaultPriceData();
                }
            }

            return $result;

        } catch (\Throwable $e) {
            Log::error('批量获取扩展价格数据失败', [
                'error' => $e->getMessage(),
                'object_ids_count' => count($objectIds)
            ]);
            
            // 返回默认数据
            $result = [];
            foreach ($objectIds as $objectId) {
                $result[$objectId] = $this->getDefaultPriceData();
            }
            return $result;
        }
    }

    /**
     * 批量获取24小时价格极值的时间戳
     */
    private function getBatchPriceTimeStamps(array $objectIds): array
    {
        if (empty($objectIds)) {
            return [];
        }

        try {
            $result = [];
            
            // 批量查询最高价时间
            $highestPriceTimestamps = Db::name('sjz_price_history')
                ->whereIn('object_id', $objectIds)
                ->where('timestamp', '>=', date('Y-m-d H:i:s', strtotime('-24 hours')))
                ->field([
                    'object_id',
                    'price',
                    'timestamp'
                ])
                ->select()
                ->toArray();
            
            // 分组并找出每个物品的最高价时间
            $highestByObject = [];
            foreach ($highestPriceTimestamps as $record) {
                $objectId = $record['object_id'];
                if (!isset($highestByObject[$objectId]) || $record['price'] > $highestByObject[$objectId]['price']) {
                    $highestByObject[$objectId] = $record;
                }
            }
            
            // 批量查询最低价时间
            $lowestByObject = [];
            foreach ($highestPriceTimestamps as $record) {
                $objectId = $record['object_id'];
                if (!isset($lowestByObject[$objectId]) || $record['price'] < $lowestByObject[$objectId]['price']) {
                    $lowestByObject[$objectId] = $record;
                }
            }
            
            // 合并结果
            foreach ($objectIds as $objectId) {
                $result[$objectId] = [
                    'highest_price_time' => $highestByObject[$objectId]['timestamp'] ?? null,
                    'lowest_price_time' => $lowestByObject[$objectId]['timestamp'] ?? null,
                ];
            }
            
            return $result;
            
        } catch (\Throwable $e) {
            Log::error('获取价格时间戳失败', [
                'error' => $e->getMessage(),
                'object_ids_count' => count($objectIds)
            ]);
            
            // 返回默认数据
            $result = [];
            foreach ($objectIds as $objectId) {
                $result[$objectId] = [
                    'highest_price_time' => null,
                    'lowest_price_time' => null,
                ];
            }
            return $result;
        }
    }

    /**
     * 获取默认价格数据
     */
    private function getDefaultPriceData(): array
    {
        return [
            'current_price' => 0,
            'day_price' => 0,
            'day_change' => 0,
            'day_change_pct' => 0,
            'week_price' => 0,
            'week_change' => 0,
            'week_change_pct' => 0,
            'month_price' => 0,
            'month_change' => 0,
            'month_change_pct' => 0,
            'highest_price_24h' => 0,
            'lowest_price_24h' => 0,
            'highest_price_7d' => 0,
            'lowest_price_7d' => 0,
            'avg_price_24h' => 0,
            'price_trend' => '平稳',
            'volatility_24h' => 0,
            'highest_price_24h_time' => null,
            'lowest_price_24h_time' => null,
        ];
    }

    /**
     * 批量获取价格数据 - 性能优化版本（保留兼容性）
     */
    private function getBatchPriceData(array $objectIds): array
    {
        if (empty($objectIds)) {
            return [];
        }

        $result = [];

        // 初始化变量
        $currentPrices = [];
        $dayPrices = [];
        $weekPrices = [];
        $monthPrices = [];
        
        try {
            // 1. 批量获取当前价格 - 使用最新价格表
            $currentPrices = Db::name('sjz_latest_prices')
                ->whereIn('object_id', $objectIds)
                ->column('current_price', 'object_id');

            // 2. 批量获取历史价格
            $dayAgo = date('Y-m-d H:i:s', strtotime('-1 day'));
            $weekAgo = date('Y-m-d H:i:s', strtotime('-7 day'));
            $monthAgo = date('Y-m-d H:i:s', strtotime('-30 day'));

            // 优化：使用更精确的日期范围和LIMIT，避免全表扫描
            $dayPrices = $this->getHistoricalPricesFast($objectIds, $dayAgo, '1天前');
            $weekPrices = $this->getHistoricalPricesFast($objectIds, $weekAgo, '7天前');
            $monthPrices = $this->getHistoricalPricesFast($objectIds, $monthAgo, '30天前');

            // 3. 合并数据
            foreach ($objectIds as $objectId) {
                $result[$objectId] = [
                    'current_price' => (float)($currentPrices[$objectId] ?? 0),
                    'day_price' => (float)($dayPrices[$objectId] ?? 0),
                    'week_price' => (float)($weekPrices[$objectId] ?? 0),
                    'month_price' => (float)($monthPrices[$objectId] ?? 0),
                ];
            }

        } catch (\Throwable $e) {
            Log::error('批量获取价格数据失败', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'object_ids_count' => count($objectIds)
            ]);
            // 返回默认数据
            foreach ($objectIds as $objectId) {
                $result[$objectId] = [
                    'current_price' => 0,
                    'day_price' => 0,
                    'week_price' => 0,
                    'month_price' => 0,
                ];
            }
        }


        return $result;
    }

    /**
     * 快速获取历史价格 - 使用扩展价格表
     */
    private function getHistoricalPricesFast(array $objectIds, string $beforeDate, string $label): array
    {
        try {
            $startTime = microtime(true);
            
            // 确定要查询的历史价格字段
            $priceField = '';
            if (strpos($label, '1天') !== false) {
                $priceField = 'price_24h_ago';
            } elseif (strpos($label, '7天') !== false) {
                $priceField = 'price_7d_ago';
            } elseif (strpos($label, '30天') !== false) {
                $priceField = 'price_30d_ago';
            } else {
                $priceField = 'price_24h_ago'; // 默认使用24小时前价格
            }
            
            // 从扩展价格表获取历史价格
            $prices = Db::name('sjz_latest_prices')
                ->whereIn('object_id', $objectIds)
                ->column($priceField, 'object_id');
            
            // 格式化数据并处理空值
            $result = [];
            foreach ($objectIds as $objectId) {
                $price = $prices[$objectId] ?? 0;
                $result[$objectId] = (float)$price;
            }
            
            $queryTime = round(microtime(true) - $startTime, 4);
            
            return $result;
            
        } catch (\Throwable $e) {
            Log::error("历史价格查询失败({$label})", [
                'error' => $e->getMessage(),
                'object_count' => count($objectIds)
            ]);
            
            // 返回默认值
            $defaultPrices = [];
            foreach ($objectIds as $objectId) {
                $defaultPrices[$objectId] = 0;
            }
            return $defaultPrices;
        }
    }

    /**
     * 获取指定物品的最新价格
     */
    private function getLatestPrice(int $objectId): float
    {
        // 首先尝试从最新价格表获取
        try {
            $price = Db::name('sjz_latest_prices')
                ->where('object_id', $objectId)
                ->value('current_price');

            if ($price !== null) {
                return (float)$price;
            }
        } catch (\Exception $e) {
            // 如果最新价格表不存在，继续使用历史表
        }

        // 从历史表获取最新价格
        $price = Db::name('sjz_price_history')
            ->where('object_id', $objectId)
            ->where('price', '>', 0)
            ->order('timestamp', 'desc')
            ->value('price');

        return $price !== null ? (float)$price : 0.0;
    }



    /**
     * 获取指定物品在若干天前最近一次记录的价格
     */
    private function getPriceBeforeDays(int $objectId, int $days): float
    {
        // 根据天数选择对应的历史价格字段
        $priceField = match($days) {
            1 => 'price_24h_ago',
            7 => 'price_7d_ago', 
            30 => 'price_30d_ago',
            default => 'price_24h_ago'
        };

        $price = Db::name('sjz_latest_prices')
            ->where('object_id', $objectId)
            ->where($priceField, '>', 0)
            ->value($priceField);

        return $price !== null ? (float)$price : 0.0;
    }

    /**
     * 获取卡包配置
     */
    private function getBulletPackageConfigs()
    {
        return [
            'grade_3' => [
                'name' => '3级弹药卡包',
                'bullets' => [
                    '5.56x45mm M855' => 200,
                    '9x39mm SP5' => 150,
                    '7.62x54R T46M' => 150,
                    '.45 ACP FMJ' => 180,
                    '5.7x28mm L191' => 200,
                    '4.6x30mm Subsonic SX' => 200,
                    '9x19mm AP6.3' => 200,
                    '.50 AE JHP' => 150,
                    '5.8x42mm DVP88' => 180,
                    '7.62x39mm PS' => 150,
                    '7.62x51mm BPZ' => 150,
                    '5.45x39mm PS' => 200,
                    '.357 Magnum JHP' => 150,
                    '12.7x55mm PS12A' => 80
                ]
            ],
            'grade_4' => [
                'name' => '4级弹药卡包',
                'bullets' => [
                    '9x39mm SP6' => 150,
                    '7.62x54R LPS' => 150,
                    '6.8x51mm FMJ' => 150,
                    '7.62x39mm BP' => 150,
                    '5.8x42mm DBP10' => 150,
                    '.45 ACP AP' => 150,
                    '5.56x45mm M855A1' => 150,
                    '7.62x51mm M80' => 150,
                    '4.6x30mm FMJ SX' => 150,
                    '5.7x28mm SS193' => 150,
                    '.357 Magnum FMJ' => 120,
                    '9x19mm PBP' => 150,
                    '.50 AE FMJ' => 120,
                    '5.45x39mm BT' => 150,
                    '12.7x55mm PD12双头弹' => 60,
                    '12.7x55mm PS12' => 60
                ]
            ],
            'grade_5' => [
                'name' => '5级弹药卡包',
                'bullets' => [
                    '5.8x42mm DVC12' => 240,
                    '5.56x45mm M995' => 240,
                    '4.6x30mm AP SX' => 240,
                    '7.62x39mm AP' => 200,
                    '6.8x51mm Hybrid' => 200,
                    '9x39mm BP' => 200,
                    '5.7x28mm SS190' => 240,
                    '5.45x39mm BS' => 240,
                    '7.62x51mm M62' => 150,
                    '7.62x54R BT' => 120
                ]
            ],
            'pass_advanced' => [
                'name' => '通行证高级子弹自选包',
                'bullets' => [
                    '5.8x42mm DBP10' => 50,
                    '9x39mm SP6' => 40,
                    '6.8x51mm FMJ' => 40,
                    '.45 ACP AP' => 45,
                    '5.56x45mm M855A1' => 45,
                    '7.62x54R LPS' => 35,
                    '7.62x39mm BP' => 40,
                    '5.7x28mm SS193' => 50,
                    '9x19mm PBP' => 50,
                    '4.6x30mm FMJ SX' => 45,
                    '7.62x51mm M80' => 40,
                    '12.7x55mm PD12双头弹' => 25,
                    '12.7x55mm PS12' => 30,
                    '5.45x39mm BT' => 45,
                    '12 Gauge独头 AP-20' => 35
                ]
            ],
            'pass_basic' => [
                'name' => '通行证基础子弹自选包',
                'bullets' => [
                    '9x39mm SP5' => 90,
                    '5.56x45mm M855' => 110,
                    '.45 ACP FMJ' => 100,
                    '5.7x28mm L191' => 110,
                    '7.62x54R T46M' => 80,
                    '5.8x42mm DVP88' => 110,
                    '7.62x39mm PS' => 90,
                    '4.6x30mm Subsonic SX' => 100,
                    '9x19mm AP6.3' => 110,
                    '7.62x51mm BPZ' => 90,
                    '5.45x39mm PS' => 100,
                    '12 Gauge 箭形弹' => 70,
                    '12.7x55mm PS12A' => 65
                ]
            ]
        ];
    }

    /**
     * 获取卡包数据
     */
    private function getBulletPackageData($packageId, $page = 1, $pageSize = 10)
    {
        $configs = $this->getBulletPackageConfigs();

        if (!isset($configs[$packageId])) {
            return ['list' => [], 'total' => 0];
        }

        $config = $configs[$packageId];
        $bulletNames = array_keys($config['bullets']);

        // 获取子弹数据
        $items = Db::name('sjz_items')
            ->alias('i')
            ->join('sjz_item_categories c', 'i.category_id = c.id')
            ->field([
                'i.object_id',
                'i.object_name as name',
                'i.pic as image_url',
                'i.grade',
                'c.second_class_cn as bullet_type'
            ])
            ->where('c.primary_class', 'ammo')
            ->whereIn('i.object_name', $bulletNames)
            ->whereNull('i.delete_time')
            ->select()
            ->toArray();

        // 添加价格信息和卡包数量
        $items = $this->addBulletPriceInfo($items);

        // 为每个子弹添加卡包数量和总价值
        foreach ($items as &$item) {
            $bulletName = $item['name'];
            if (isset($config['bullets'][$bulletName])) {
                $item['quantity'] = (int)$config['bullets'][$bulletName];
                $item['total_value'] = (float)($item['current_price'] * $item['quantity']);
            } else {
                $item['quantity'] = 0;
                $item['total_value'] = 0.0;
            }
        }

        // 按总价值排序
        usort($items, function($a, $b) {
            return $b['total_value'] <=> $a['total_value'];
        });

        // 如果需要分页
        if ($page > 0 && $pageSize > 0) {
            $total = count($items);
            $offset = ($page - 1) * $pageSize;
            $pagedItems = array_slice($items, $offset, $pageSize);

            return [
                'list' => $pagedItems,
                'total' => $total,
                'page' => $page,
                'page_size' => $pageSize
            ];
        }

        return [
            'list' => $items,
            'total' => count($items)
        ];
    }

    /**
     * 为子弹数据添加价格信息
     */
    private function addBulletPriceInfo($items)
    {
        if (empty($items)) {
            return $items;
        }

        $objectIds = array_column($items, 'object_id');

        // 获取当前价格
        $currentPrices = Db::name('sjz_latest_prices')
            ->whereIn('object_id', $objectIds)
            ->column('current_price', 'object_id');

        // 为每个子弹添加价格信息
        foreach ($items as &$item) {
            $objectId = $item['object_id'];
            $currentPrice = $currentPrices[$objectId] ?? 0;

            $item['current_price'] = (float)$currentPrice;
        }

        return $items;
    }
}