<?php

namespace app\common\model\sjz\item;

use think\Model;
use think\facade\Db;
use think\facade\Log;

/**
 * 最新价格模型
 * 对应表：ba_sjz_latest_prices
 */
class LatestPrices extends Model
{
    protected $name = 'sjz_latest_prices';
    protected $table = 'ba_sjz_latest_prices';
    
    // 主键
    protected $pk = 'object_id';
    
    // 字段类型
    protected $type = [
        'object_id' => 'integer',
        'current_price' => 'float',
        'price_24h_ago' => 'float',
        'last_update_timestamp' => 'datetime',
        'update_time' => 'datetime',
    ];
    
    // 自动时间戳
    protected $autoWriteTimestamp = 'datetime';
    protected $createTime = false; // 该表没有create_time字段
    protected $updateTime = 'update_time';
    
    // JSON字段
    protected $json = [];
    
    // 软删除（该表不使用软删除）
    protected $deleteTime = false;
    
    /**
     * 关联物品信息
     */
    public function item()
    {
        return $this->belongsTo(\app\common\model\sjz\Items::class, 'object_id', 'object_id');
    }
    
    /**
     * 批量获取最新价格
     * 
     * @param array $objectIds 物品ID数组
     * @return array 物品ID到价格信息的映射
     */
    public static function getBatchPrices(array $objectIds): array
    {
        if (empty($objectIds)) {
            return [];
        }
        
        $startTime = microtime(true);
        
        try {
            $results = self::whereIn('object_id', $objectIds)
                          ->field(['object_id', 'current_price', 'price_24h_ago', 'last_update_timestamp'])
                          ->select()
                          ->toArray();
            
            $priceMap = [];
            foreach ($results as $item) {
                $priceMap[$item['object_id']] = [
                    'current_price' => round((float)$item['current_price'], 2),
                    'price_24h_ago' => $item['price_24h_ago'] !== null ? round((float)$item['price_24h_ago'], 2) : null,
                    'last_update_timestamp' => $item['last_update_timestamp']
                ];
            }
            
            $executionTime = round((microtime(true) - $startTime) * 1000, 2);
            Log::info("LatestPrices::getBatchPrices 执行时间: {$executionTime}ms, 查询数量: " . count($objectIds));
            
            return $priceMap;
            
        } catch (\Exception $e) {
            Log::error('LatestPrices::getBatchPrices 异常：' . $e->getMessage());
            return [];
        }
    }
    
    /**
     * 获取单个物品的最新价格
     * 
     * @param int $objectId 物品ID
     * @return array|null 价格信息
     */
    public static function getPrice(int $objectId): ?array
    {
        try {
            $result = self::where('object_id', $objectId)
                         ->field(['object_id', 'current_price', 'price_24h_ago', 'last_update_timestamp'])
                         ->find();
            
            if (!$result) {
                return null;
            }
            
            return [
                'current_price' => round((float)$result['current_price'], 2),
                'price_24h_ago' => $result['price_24h_ago'] !== null ? round((float)$result['price_24h_ago'], 2) : null,
                'last_update_timestamp' => $result['last_update_timestamp']
            ];
            
        } catch (\Exception $e) {
            Log::error('LatestPrices::getPrice 异常：' . $e->getMessage());
            return null;
        }
    }
    
    /**
     * 更新或插入价格记录
     * 
     * @param int $objectId 物品ID
     * @param float $currentPrice 当前价格
     * @param string $timestamp 价格时间戳
     * @return bool
     */
    public static function updatePrice(int $objectId, float $currentPrice, string $timestamp): bool
    {
        try {
            $data = [
                'object_id' => $objectId,
                'current_price' => $currentPrice,
                'last_update_timestamp' => $timestamp,
                'update_time' => date('Y-m-d H:i:s')
            ];
            
            // 检查记录是否存在，决定使用update还是create
            $existingRecord = self::where('object_id', $objectId)->find();
            
            if ($existingRecord) {
                // 记录存在，更新
                $result = self::where('object_id', $objectId)->update([
                    'current_price' => $currentPrice,
                    'last_update_timestamp' => $timestamp,
                    'update_time' => date('Y-m-d H:i:s')
                ]);
                return $result > 0; // update返回影响的行数
            } else {
                // 记录不存在，创建
                $result = self::create($data);
                return $result !== false && $result !== null; // create返回模型实例或false
            }
            
        } catch (\Exception $e) {
            Log::error('LatestPrices::updatePrice 异常：' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 批量更新24小时前价格
     * 
     * @param array $priceData 价格数据数组 [object_id => price_24h_ago]
     * @return bool
     */
    public static function batchUpdate24hPrices(array $priceData): bool
    {
        if (empty($priceData)) {
            return true;
        }
        
        try {
            $updateCount = 0;
            foreach ($priceData as $objectId => $price24hAgo) {
                $result = self::where('object_id', $objectId)
                             ->update([
                                 'price_24h_ago' => $price24hAgo,
                                 'update_time' => date('Y-m-d H:i:s')
                             ]);
                if ($result) {
                    $updateCount++;
                }
            }
            
            
            return true;
            
        } catch (\Exception $e) {
            Log::error('LatestPrices::batchUpdate24hPrices 异常：' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 获取价格统计信息
     * 
     * @return array
     */
    public static function getPriceStats(): array
    {
        try {
            $stats = self::field([
                'COUNT(*) as total_items',
                'AVG(current_price) as avg_price',
                'MIN(current_price) as min_price',
                'MAX(current_price) as max_price',
                'COUNT(CASE WHEN price_24h_ago IS NOT NULL THEN 1 END) as items_with_24h_price'
            ])->find();
            
            return [
                'total_items' => (int)$stats['total_items'],
                'avg_price' => round((float)$stats['avg_price'], 2),
                'min_price' => round((float)$stats['min_price'], 2),
                'max_price' => round((float)$stats['max_price'], 2),
                'items_with_24h_price' => (int)$stats['items_with_24h_price']
            ];
            
        } catch (\Exception $e) {
            Log::error('LatestPrices::getPriceStats 异常：' . $e->getMessage());
            return [];
        }
    }
    
    /**
     * 获取价格变化最大的物品
     * 
     * @param int $limit 返回数量限制
     * @param string $order 排序方式 'asc'|'desc'
     * @return array
     */
    public static function getTopPriceChanges(int $limit = 10, string $order = 'desc'): array
    {
        try {
            $orderBy = $order === 'asc' ? 'ASC' : 'DESC';
            
            $tableName = (new self())->getTable();
            $sql = "SELECT 
                        object_id,
                        current_price,
                        price_24h_ago,
                        CASE 
                            WHEN price_24h_ago > 0 
                            THEN ((current_price - price_24h_ago) / price_24h_ago) * 100
                            ELSE 0
                        END as price_change_percent
                    FROM {$tableName}
                    WHERE price_24h_ago IS NOT NULL AND price_24h_ago > 0
                    ORDER BY price_change_percent {$orderBy}
                    LIMIT {$limit}";
            
            $results = Db::query($sql);
            
            foreach ($results as &$item) {
                $item['current_price'] = round((float)$item['current_price'], 2);
                $item['price_24h_ago'] = round((float)$item['price_24h_ago'], 2);
                $item['price_change_percent'] = round((float)$item['price_change_percent'], 2);
            }
            
            return $results;
            
        } catch (\Exception $e) {
            Log::error('LatestPrices::getTopPriceChanges 异常：' . $e->getMessage());
            return [];
        }
    }
    
    /**
     * 清理过期的价格记录（如果需要）
     * 
     * @param int $days 保留天数
     * @return bool
     */
    public static function cleanupOldRecords(int $days = 30): bool
    {
        try {
            // 注意：这个表通常不需要清理，因为它只保存最新价格
            // 这个方法主要是为了完整性，实际使用中可能不需要
            $cutoffDate = date('Y-m-d H:i:s', strtotime("-{$days} days"));
            
            $deletedCount = self::where('update_time', '<', $cutoffDate)->delete();
            Log::info("LatestPrices::cleanupOldRecords 清理了 {$deletedCount} 条过期记录");
            
            return true;
            
        } catch (\Exception $e) {
            Log::error('LatestPrices::cleanupOldRecords 异常：' . $e->getMessage());
            return false;
        }
    }
}
