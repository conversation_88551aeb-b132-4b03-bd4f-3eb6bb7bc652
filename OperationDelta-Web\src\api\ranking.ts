/**
 * 排行榜相关API
 */
import { apiClient } from './index'
import type { ApiResponse } from './types/common'
import type { RankingParams, RankingResponse } from '@/types/ranking'
// 移除了RequestPriority导入（队列系统已移除）

// 排行榜API端点
export const RANKING_API = {
  GET_RANKING_LIST: '/ranking/getRankingList',
  // 钥匙卡排行榜接口
  GET_KEYCARD_RANKING: '/ranking/getKeycardRanking',
  // 子弹排行榜接口
  GET_BULLET_RANKING: '/ranking/getBulletRanking',
  // 子弹卡包排行榜接口
  GET_BULLET_PACKAGE_RANKING: '/ranking/getBulletPackageRanking',
  // 子弹价格查询接口
  GET_BULLET_PRICES: '/ranking/getBulletPrices'
}

/**
 * 获取排行榜数据 - 使用POST请求确保兼容性，设置高优先级
 * @param params 排行榜请求参数
 */
export function getRankingList(params: Partial<RankingParams> = {}) {
  const requestParams = {
    type: params.type || 'highest_price',
    time_range: params.time_range || 'day',
    page: params.page || 1,
    page_size: params.page_size || 20,
    grade: params.grade || undefined,
    item_type: params.item_type || undefined,
    min_price: params.min_price || undefined,
    max_price: params.max_price || undefined
  }

  // 使用POST请求确保兼容性
  return apiClient.post<ApiResponse<RankingResponse>>(RANKING_API.GET_RANKING_LIST, requestParams)
}

/**
 * 获取钥匙卡排行榜数据 - 使用POST请求
 * @param params 分页、排序与筛选参数
 */
export function getKeycardRanking(params: { 
  page?: number; 
  page_size?: number; 
  sort?: string;
  grade?: number;
  min_price?: number;
  max_price?: number;
} = {}) {
  // 默认参数
  const requestParams: any = {
    page: params.page || 1,
    page_size: params.page_size || 20,
    sort: params.sort || 'current_price_desc'
  }
  
  // 可选筛选参数
  if (params.grade && params.grade > 0) {
    requestParams.grade = params.grade
  }
  if (params.min_price && params.min_price > 0) {
    requestParams.min_price = params.min_price
  }
  if (params.max_price && params.max_price > 0) {
    requestParams.max_price = params.max_price
  }

  // 使用POST请求
  return apiClient.post<ApiResponse<any>>(RANKING_API.GET_KEYCARD_RANKING, requestParams)
}

/**
 * 获取子弹排行榜数据 - 使用POST请求
 * @param params 分页、排序与筛选参数
 */
export function getBulletRanking(params: { 
  page?: number; 
  page_size?: number; 
  sort?: string;
  grade?: number;
  min_price?: number;
  max_price?: number;
  bullet_type?: string;
} = {}) {
  // 默认参数
  const requestParams: any = {
    page: params.page || 1,
    page_size: params.page_size || 20,
    sort: params.sort || 'current_price_desc'
  }
  
  // 可选筛选参数
  if (params.grade && params.grade > 0) {
    requestParams.grade = params.grade
  }
  if (params.min_price && params.min_price > 0) {
    requestParams.min_price = params.min_price
  }
  if (params.max_price && params.max_price > 0) {
    requestParams.max_price = params.max_price
  }
  if (params.bullet_type) {
    requestParams.item_type = params.bullet_type
  }

  // 使用POST请求
  return apiClient.post<ApiResponse<any>>(RANKING_API.GET_BULLET_RANKING, requestParams)
}

/**
 * 获取子弹卡包排行榜数据 - 使用POST请求
 * @param params 分页与排序参数
 */
export function getBulletPackageRanking(params: { grade?: number; page?: number; page_size?: number } = {}) {
  // 默认参数
  const requestParams = {
    grade: params.grade || 0, // 0表示获取所有等级
    page: params.page || 1,
    page_size: params.page_size || 10
  }

  // 使用POST请求
  return apiClient.post<ApiResponse<any>>(RANKING_API.GET_BULLET_PACKAGE_RANKING, requestParams)
}

/**
 * 获取子弹价格数据 - 通用接口，供前端配置驱动使用
 * @param params 查询参数
 */
export function getBulletPrices(params: { object_ids?: string; grades?: string } = {}) {
  // 默认参数
  const requestParams = {
    object_ids: params.object_ids || '', // 子弹ID列表，逗号分隔
    grades: params.grades || '' // 等级列表，逗号分隔
  }

  // 使用POST请求
  return apiClient.post<ApiResponse<any>>(RANKING_API.GET_BULLET_PRICES, requestParams)
}

/**
 * 排行榜类型配置
 */
export const RANKING_TYPES = {
  highest_price: {
    label: '最高价格',
    description: '按当前价格从高到低排序',
    icon: 'trending-up',
    color: '#f56c6c'
  },
  lowest_price: {
    label: '最低价格', 
    description: '按当前价格从低到高排序',
    icon: 'trending-down',
    color: '#67c23a'
  },
  increase_percentage: {
    label: '涨幅榜',
    description: '按价格涨幅百分比排序',
    icon: 'arrow-up',
    color: '#e6a23c'
  },
  decrease_percentage: {
    label: '跌幅榜',
    description: '按价格跌幅百分比排序', 
    icon: 'arrow-down',
    color: '#409eff'
  },
  price_change_absolute: {
    label: '波动榜',
    description: '按价格变化绝对值排序',
    icon: 'swap-horizontal',
    color: '#909399'
  },
  price_change_max: {
    label: '上涨金额',
    description: '按价格上涨金额排序',
    icon: 'cash',
    color: '#f56c6c'
  },
  price_change_min: {
    label: '下跌金额',
    description: '按价格下跌金额排序',
    icon: 'cash-outline',
    color: '#67c23a'
  }
} as const

/**
 * 时间范围配置
 */
export const TIME_RANGES = {
  hour: {
    label: '1小时',
    description: '最近1小时的价格变化'
  },
  day: {
    label: '今日',
    description: '今日的价格变化'
  },
  week: {
    label: '7天',
    description: '最近7天的价格变化'
  },
  month: {
    label: '30天',
    description: '最近30天的价格变化'
  }
} as const

/**
 * 获取排行榜菜单配置
 */
export const getRankingMenuConfig = () => {
  return [
    {
      title: '价格排行',
      items: [
        {
          key: 'highest_price' as const,
          ...RANKING_TYPES.highest_price
        },
        {
          key: 'lowest_price' as const,
          ...RANKING_TYPES.lowest_price
        }
      ]
    },
    {
      title: '涨跌排行',
      items: [
        {
          key: 'increase_percentage' as const,
          ...RANKING_TYPES.increase_percentage
        },
        {
          key: 'decrease_percentage' as const,
          ...RANKING_TYPES.decrease_percentage
        },
        {
          key: 'price_change_absolute' as const,
          ...RANKING_TYPES.price_change_absolute
        }
      ]
    },
    {
      title: '金额变化',
      items: [
        {
          key: 'price_change_max' as const,
          ...RANKING_TYPES.price_change_max
        },
        {
          key: 'price_change_min' as const,
          ...RANKING_TYPES.price_change_min
        }
      ]
    }
  ]
}
