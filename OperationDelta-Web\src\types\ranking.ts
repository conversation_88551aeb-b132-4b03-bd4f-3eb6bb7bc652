/**
 * 排行榜相关类型定义
 */

// 排行榜类型枚举
export type RankingType = 
  | 'highest_price'           // 最高价格
  | 'lowest_price'            // 最低价格  
  | 'increase_percentage'     // 涨幅百分比
  | 'decrease_percentage'     // 跌幅百分比
  | 'price_change_absolute'   // 价格变化绝对值
  | 'price_change_max'        // 价格上涨金额
  | 'price_change_min'        // 价格下跌金额

// 时间范围类型
export type TimeRange = 'hour' | 'day' | 'week' | 'month'

// 排行榜物品数据接口
export interface RankingItem {
  object_id: number
  name: string
  image_url: string
  grade: number
  width: number
  length: number
  primary_class: string
  second_class: string
  current_price: number
  previous_price: number
  price_change: number
  price_change_percentage: number
  trend: 'up' | 'down' | 'stable'
  unit_price: number
  lowest_price?: number
  highest_price?: number
  average_price?: number
  // 新增字段
  desc?: string
  category_id?: number
  weight?: string
  pic?: string
  pre_pic?: string
  create_time?: string
  update_time?: string
  delete_time?: string | null
  avg_price?: string
  min_price?: string
  max_price?: string
  // 比较数据
  compare_to_average?: {
    difference: number
    percentage: number
  }
  compare_to_highest?: {
    difference: number
    percentage: number
  }
  compare_to_lowest?: {
    difference: number
    percentage: number
  }
}

// 排行榜请求参数
export interface RankingParams {
  type?: RankingType
  time_range?: TimeRange
  page?: number
  page_size?: number
  grade?: number
  item_type?: string
  min_price?: number
  max_price?: number
}

// 排行榜响应数据
export interface RankingResponse {
  list: RankingItem[]
  total: number
  page: number
  page_size: number
  query_time: number
}

// 排行榜菜单项配置
export interface RankingMenuItem {
  key: RankingType
  label: string
  description: string
  icon: string
  color: string
}

// 排行榜筛选器
export interface RankingFilter {
  type: RankingType
  timeRange: TimeRange
  page: number
  pageSize: number
  grade: number
  itemType: string
  minPrice: number | null
  maxPrice: number | null
}

// 分类排行榜相关接口
export interface CategoryRankingItem {
  primary_class: string
  primary_class_cn?: string
  total_items: number
  avg_price?: number
  min_price?: number
  max_price?: number
  price_change_24h?: number
  children?: {
    id: number
    category_key: string
    second_class: string
    second_class_cn: string
    item_count: number
  }[]
}

// 分类统计数据
export interface CategoryStats {
  category: string
  category_name: string
  item_count: number
  avg_price: number
  min_price: number
  max_price: number
  price_trend: 'up' | 'down' | 'stable'
  price_change_percentage: number
}

// 排行榜菜单分组
export interface RankingMenuGroup {
  title: string
  items: RankingMenuItem[]
}

// ---------------- 钥匙卡排行榜相关类型 ----------------

/**
 * 钥匙卡排行榜单条记录
 */
export interface KeycardRankingItem {
  object_id: number
  name: string
  image_url: string
  grade: number
  current_price: number
  day_price: number
  day_change: number
  day_change_pct: number
  week_price: number
  week_change: number
  week_change_pct: number
  month_price: number
  month_change: number
  month_change_pct: number
  // 扩展数据字段
  highest_price_24h?: number
  lowest_price_24h?: number
  highest_price_7d?: number
  lowest_price_7d?: number
  avg_price_24h?: number
  price_trend?: string
  volatility_24h?: number
  price_vs_24h_high_pct?: number
  price_vs_24h_low_pct?: number
  highest_price_24h_time?: string
  lowest_price_24h_time?: string
}

/**
 * 钥匙卡排行榜响应结构
 */
export interface KeycardRankingResponse {
  list: KeycardRankingItem[]
  total: number
  page: number
  page_size: number
}

// ---------------- 子弹排行榜相关类型 ----------------

/**
 * 子弹排行榜单条记录
 */
export interface BulletRankingItem {
  object_id: number
  name: string
  image_url: string
  grade: number
  bullet_type: string
  current_price: number
  day_price: number
  day_change: number
  day_change_pct: number
  week_price: number
  week_change: number
  week_change_pct: number
  month_price: number
  month_change: number
  month_change_pct: number
  // 扩展数据字段
  highest_price_24h?: number
  lowest_price_24h?: number
  highest_price_7d?: number
  lowest_price_7d?: number
  avg_price_24h?: number
  price_trend?: string
  volatility_24h?: number
  price_vs_24h_high_pct?: number
  price_vs_24h_low_pct?: number
  highest_price_24h_time?: string
  lowest_price_24h_time?: string
}

/**
 * 子弹排行榜响应结构
 */
export interface BulletRankingResponse {
  list: BulletRankingItem[]
  total: number
  page: number
  page_size: number
}

// ---------------- 子弹卡包排行榜相关类型 ----------------

/**
 * 子弹卡包排行榜单条记录
 */
export interface BulletPackageRankingItem {
  object_id: number
  name: string
  image_url: string
  grade: number
  bullet_type: string
  current_price: number
  quantity: number
  total_value: number
}

/**
 * 子弹卡包排行榜响应结构
 */
export interface BulletPackageRankingResponse {
  list: BulletPackageRankingItem[]
  total: number
  page: number
  page_size: number
}

// ---------------- 子弹价格查询相关类型 ----------------

/**
 * 子弹价格数据
 */
export interface BulletPriceItem {
  object_id: number
  name: string
  image_url: string
  grade: number
  bullet_type: string
  current_price: number
}

/**
 * 子弹价格查询响应结构
 */
export interface BulletPriceResponse {
  list: BulletPriceItem[]
  total: number
}
