<template>
  <div class="keycard-ranking">
    <!-- 顶部标题 -->
    <div class="header">
      <n-space align="center" :size="10">
        <n-icon :size="26" color="#d97706">
          <LockClosedOutline />
        </n-icon>
        <n-text strong style="font-size: 20px;">钥匙卡排行榜</n-text>
      </n-space>
    </div>

    <!-- 筛选工具栏 -->
    <div class="filter-section">
      <n-space :size="12" align="center">
        <!-- 等级筛选 -->
        <div class="filter-item">
          <n-text style="font-size: 14px; color: var(--n-text-color-2); margin-right: 8px;">等级:</n-text>
          <n-select
            v-model:value="filters.grade"
            :options="gradeOptions"
            style="width: 120px;"
            size="small"
            @update:value="handleFilterChange"
          />
        </div>
        
        <!-- 排序方式 -->
        <div class="filter-item">
          <n-text style="font-size: 14px; color: var(--n-text-color-2); margin-right: 8px;">排序:</n-text>
          <n-select
            v-model:value="filters.sort"
            :options="sortOptions"
            style="width: 140px;"
            size="small"
            @update:value="handleFilterChange"
          />
        </div>
        
        <!-- 价格范围筛选 -->
        <div class="filter-item">
          <n-text style="font-size: 14px; color: var(--n-text-color-2); margin-right: 8px;">价格:</n-text>
          <n-input-group compact style="width: 200px;">
            <n-input-number
              v-model:value="filters.minPrice"
              placeholder="最低价"
              size="small"
              style="width: 50%;"
              :precision="0"
              :min="0"
              @update:value="handlePriceRangeChange"
            />
            <n-input-number
              v-model:value="filters.maxPrice"
              placeholder="最高价"
              size="small"
              style="width: 50%;"
              :precision="0"
              :min="0"
              @update:value="handlePriceRangeChange"
            />
          </n-input-group>
        </div>
        
        <!-- 重置按钮 -->
        <n-button size="small" @click="resetFilters" type="tertiary">
          <template #icon>
            <n-icon><RefreshOutline /></n-icon>
          </template>
          重置
        </n-button>
      </n-space>
    </div>

    <!-- 排行榜列表区域 -->
    <div class="ranking-section">
      <!-- 加载骨架屏 -->
      <LoadingSkeleton v-if="loading" type="list" :count="pageSize" />

      <!-- 空状态 -->
      <div v-else-if="!items.length" class="empty-wrapper">
        <n-empty description="暂无排行数据">
          <template #icon>
            <n-icon :size="48" :depth="3">
              <LockClosedOutline />
            </n-icon>
          </template>
        </n-empty>
      </div>

      <!-- 排行榜表格 -->
      <n-data-table
        v-else
        :columns="isMobile ? mobileOptimizedColumns : columns"
        :data="items"
        :pagination="false"
        :bordered="false"
        :scroll-x="isMobile ? 1200 : 1400"
        size="small"
      />

      <!-- 分页 -->
      <div class="pagination-wrapper" v-if="total > pageSize">
        <n-pagination
          :page="page"
          :page-size="pageSize"
          :item-count="total"
          show-size-picker
          :page-sizes="[10, 20, 30, 50]"
          @update:page="onPageChange"
          @update:page-size="onPageSizeChange"
        />
      </div>
    </div>

    <!-- 物品详情弹窗 -->
    <ItemDetailModal
      v-model:show="showDetailModal"
      :object-id="selectedItemId"
    />
  </div>
</template>

<script setup lang="ts">
// --------------- 依赖导入 ---------------
import { ref, onMounted, onUnmounted, h, computed } from 'vue'
import { NDataTable, NPagination, NIcon, NText, NEmpty, NAvatar, NSpace, NSelect, NInputNumber, NInputGroup, NButton } from 'naive-ui'
import { LockClosedOutline, RefreshOutline } from '@vicons/ionicons5'
import { getKeycardRanking } from '@/api/ranking'
import type { DataTableColumns } from 'naive-ui'
import type { KeycardRankingResponse, KeycardRankingItem } from '@/types/ranking'
import LoadingSkeleton from '../../../components/common/LoadingSkeleton.vue'
import ItemDetailModal from '../../../components/ItemDetailModal.vue'

// --------------- 类型定义 ---------------
interface KeycardItem {
  object_id: number
  name: string
  image_url: string
  grade: number
  current_price: number
  day_price: number
  day_change: number
  day_change_pct: number
  week_price: number
  week_change: number
  week_change_pct: number
  month_price: number
  month_change: number
  month_change_pct: number
}

// --------------- 响应式状态 ---------------
const loading = ref(false)
const items = ref<KeycardItem[]>([])
const page = ref(1)
const pageSize = ref(20)
const total = ref(0)

// 详情弹窗相关状态
const showDetailModal = ref(false)
const selectedItemId = ref<number | null>(null)

// 移动端检测
const isMobile = ref(false)

// 筛选状态
const filters = ref({
  grade: -1, // -1表示全部等级
  sort: 'current_price_desc',
  minPrice: null as number | null,
  maxPrice: null as number | null
})

// 筛选选项配置
const gradeOptions = [
  { label: '全部等级', value: -1 }, // 使用-1表示全部等级
  { label: '1级 (普通)', value: 1 },  // 白色/灰色
  { label: '2级 (优秀)', value: 2 },  // 绿色
  { label: '3级 (稀有)', value: 3 },  // 蓝色
  { label: '4级 (史诗)', value: 4 },  // 紫色
  { label: '5级 (传说)', value: 5 },  // 橙色
  { label: '6级 (神话)', value: 6 }   // 红色 - 最高级
]

const sortOptions = [
  { label: '价格从高到低', value: 'current_price_desc' },
  { label: '价格从低到高', value: 'current_price_asc' },
  { label: '今日涨幅最大', value: 'day_change_desc' },
  { label: '今日跌幅最大', value: 'day_change_asc' },
  { label: '7日涨幅最大', value: 'week_change_desc' },
  { label: '波动率最高', value: 'volatility_desc' }
]

const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768
}

onMounted(() => {
  checkMobile()
  window.addEventListener('resize', checkMobile)
  fetchData() // 加载数据
})

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile)
  // 清理定时器
  if (priceFilterTimer.value) {
    clearTimeout(priceFilterTimer.value)
  }
})

// --------------- 表格列定义 ---------------
const columns: DataTableColumns<KeycardRankingItem> = [
  {
    title: '排名',
    key: 'rank',
    width: 70,
    render: (_row, index) => {
      return index + 1 + (page.value - 1) * pageSize.value
    }
  },
  {
    title: '物品',
    key: 'name',
    render: (row) => {
      // 根据等级添加全局边框class
      return h('div', {
        style: {
          display: 'flex',
          alignItems: 'center',
          gap: '8px',
          cursor: 'pointer'
        },
        onClick: () => showItemDetail(row.object_id)
      }, [
        h(NAvatar, {
          size: 36,
          src: row.image_url,
          class: `grade-bg-${row.grade}`,
          style: {
            background: 'var(--n-card-color)',
            marginRight: '4px'
          }
        }),
        h('span', { style: { fontWeight: 500 } }, row.name)
      ])
    }
  },
  {
    title: '当前价格',
    key: 'current_price',
    render: (row) => `${row.current_price.toFixed(2)}`
  },
  // 今日涨幅列
  {
    title: '今日涨幅',
    key: 'day_change',
    render: (row) => {
      const color = row.day_change > 0 ? '#f56c6c' : row.day_change < 0 ? '#67c23a' : '#909399'
      return h('span', { style: { color, fontWeight: 500 } }, `${row.day_change.toFixed(2)} ( ${row.day_change_pct.toFixed(2)}% )`)
    }
  },
  // 7 日数据
  {
    title: '7日价格',
    key: 'week_price',
    render: (row) => row.week_price.toFixed(2)
  },
  {
    title: '7日涨幅',
    key: 'week_change',
    render: (row) => {
      const color = row.week_change > 0 ? '#f56c6c' : row.week_change < 0 ? '#67c23a' : '#909399'
      return h('span', { style: { color, fontWeight: 500 } }, `${row.week_change.toFixed(2)} ( ${row.week_change_pct.toFixed(2)}% )`)
    }
  },
  // 30 日数据
  {
    title: '30日价格',
    key: 'month_price',
    render: (row) => row.month_price.toFixed(2)
  },
  {
    title: '30日涨幅',
    key: 'month_change',
    render: (row) => {
      const color = row.month_change > 0 ? '#f56c6c' : row.month_change < 0 ? '#67c23a' : '#909399'
      return h('span', { style: { color, fontWeight: 500 } }, `${row.month_change.toFixed(2)} ( ${row.month_change_pct.toFixed(2)}% )`)
    }
  },
  // 扩展数据：24小时价格区间（带时间）
  {
    title: '24h区间 (价格/时间)',
    key: 'price_range_24h',
    width: 180,
    render: (row) => {
      const formatTime = (timestamp) => {
        if (!timestamp) return '未知'
        const date = new Date(timestamp)
        return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`
      }
      
      return h('div', { style: { fontSize: '11px', lineHeight: '1.3' } }, [
        h('div', { style: { color: '#f56c6c', marginBottom: '2px' } }, 
          `高: ${row.highest_price_24h?.toFixed(2) || 0}(${formatTime(row.highest_price_24h_time)})`
        ),
        h('div', { style: { color: '#67c23a' } }, 
          `低: ${row.lowest_price_24h?.toFixed(2) || 0}(${formatTime(row.lowest_price_24h_time)})`
        )
      ])
    }
  },
  // 扩展数据：价格趋势
  {
    title: '趋势',
    key: 'price_trend',
    render: (row) => {
      const trendColor = row.price_trend === '上涨' ? '#f56c6c' : 
                        row.price_trend === '下跌' ? '#67c23a' : '#909399'
      return h('span', { 
        style: { 
          color: trendColor, 
          fontWeight: 500,
          fontSize: '12px'
        } 
      }, row.price_trend || '平稳')
    }
  },
  // 扩展数据：波动率
  {
    title: '波动率',
    key: 'volatility_24h',
    render: (row) => {
      const volatility = row.volatility_24h || 0
      const color = volatility > 10 ? '#f56c6c' : volatility > 5 ? '#e6a23c' : '#67c23a'
      return h('span', { style: { color, fontSize: '12px' } }, `${volatility.toFixed(2)}%`)
    }
  }
]

// 移动端列配置 - 只显示最重要的信息
const mobileColumns: DataTableColumns<KeycardRankingItem> = [
  {
    title: '排名',
    key: 'rank',
    width: 50,
    render: (_row, index) => {
      return index + 1 + (page.value - 1) * pageSize.value
    }
  },
  {
    title: '钥匙卡',
    key: 'name',
    width: 140,
    render: (row) => {
      return h('div', {
        style: {
          display: 'flex',
          alignItems: 'center',
          gap: '6px',
          cursor: 'pointer'
        },
        onClick: () => showItemDetail(row.object_id)
      }, [
        h(NAvatar, {
          size: 28,
          src: row.image_url,
          class: `grade-bg-${row.grade}`,
          style: {
            background: 'var(--n-card-color)',
            marginRight: '2px'
          }
        }),
        h('div', { style: { fontSize: '12px', fontWeight: 500, lineHeight: '1.2' } }, row.name)
      ])
    }
  },
  {
    title: '当前价格',
    key: 'current_price',
    width: 80,
    render: (row) => h('div', { style: { fontSize: '12px', fontWeight: 500 } }, row.current_price.toFixed(0))
  },
  {
    title: '今日',
    key: 'day_change_mobile',
    width: 90,
    render: (row) => {
      const color = row.day_change > 0 ? '#f56c6c' : row.day_change < 0 ? '#67c23a' : '#909399'
      return h('div', { style: { fontSize: '11px', lineHeight: '1.2' } }, [
        h('div', { style: { color, fontWeight: 500 } }, `${row.day_change_pct.toFixed(1)}%`),
        h('div', { style: { color: 'var(--n-text-color-3)', fontSize: '10px' } }, row.day_change.toFixed(0))
      ])
    }
  },
  {
    title: '7日',
    key: 'week_change_mobile',
    width: 90,
    render: (row) => {
      const color = row.week_change > 0 ? '#f56c6c' : row.week_change < 0 ? '#67c23a' : '#909399'
      return h('div', { style: { fontSize: '11px', lineHeight: '1.2' } }, [
        h('div', { style: { color, fontWeight: 500 } }, `${row.week_change_pct.toFixed(1)}%`),
        h('div', { style: { color: 'var(--n-text-color-3)', fontSize: '10px' } }, row.week_change.toFixed(0))
      ])
    }
  },
  {
    title: '24h区间',
    key: 'range_mobile',
    width: 100,
    render: (row) => {
      const formatTime = (timestamp) => {
        if (!timestamp) return '未知'
        const date = new Date(timestamp)
        return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`
      }
      
      return h('div', { style: { fontSize: '9px', lineHeight: '1.2' } }, [
        h('div', { style: { color: '#f56c6c', marginBottom: '1px' } }, 
          `高${row.highest_price_24h?.toFixed(0) || 0}(${formatTime(row.highest_price_24h_time)})`
        ),
        h('div', { style: { color: '#67c23a' } }, 
          `低${row.lowest_price_24h?.toFixed(0) || 0}(${formatTime(row.lowest_price_24h_time)})`
        )
      ])
    }
  },
  {
    title: '趋势',
    key: 'trend_mobile',
    width: 40,
    render: (row) => {
      const trend = row.price_trend || '平稳'
      const trendColor = trend === '上涨' ? '#f56c6c' : trend === '下跌' ? '#67c23a' : '#909399'
      const trendIcon = trend === '上涨' ? '↗' : trend === '下跌' ? '↘' : '→'
      return h('span', { 
        style: { 
          color: trendColor, 
          fontSize: '14px',
          fontWeight: 500
        } 
      }, trendIcon)
    }
  }
]

// 移动端优化列配置 - 显示所有列但优化尺寸
const mobileOptimizedColumns: DataTableColumns<KeycardRankingItem> = [
  {
    title: '#',
    key: 'rank',
    width: 40,
    fixed: 'left',
    render: (_row, index) => {
      return index + 1 + (page.value - 1) * pageSize.value
    }
  },
  {
    title: '钥匙卡',
    key: 'name',
    width: 120,
    fixed: 'left',
    render: (row) => {
      return h('div', {
        style: {
          display: 'flex',
          alignItems: 'center',
          gap: '4px',
          cursor: 'pointer'
        },
        onClick: () => showItemDetail(row.object_id)
      }, [
        h(NAvatar, {
          size: 24,
          src: row.image_url,
          class: `grade-bg-${row.grade}`,
          style: {
            background: 'var(--n-card-color)',
            marginRight: '2px'
          }
        }),
        h('div', { style: { fontSize: '11px', fontWeight: 500 } }, row.name)
      ])
    }
  },
  {
    title: '当前价格',
    key: 'current_price',
    width: 80,
    render: (row) => h('div', { style: { fontSize: '11px', fontWeight: 500 } }, row.current_price.toFixed(0))
  },
  {
    title: '今日涨幅',
    key: 'day_change',
    width: 85,
    render: (row) => {
      const color = row.day_change > 0 ? '#f56c6c' : row.day_change < 0 ? '#67c23a' : '#909399'
      return h('div', { style: { fontSize: '10px', lineHeight: '1.2' } }, [
        h('div', { style: { color, fontWeight: 500 } }, `${row.day_change_pct.toFixed(1)}%`),
        h('div', { style: { color: 'var(--n-text-color-3)', fontSize: '9px' } }, row.day_change.toFixed(0))
      ])
    }
  },
  {
    title: '7日价格',
    key: 'week_price',
    width: 70,
    render: (row) => h('div', { style: { fontSize: '11px' } }, row.week_price.toFixed(0))
  },
  {
    title: '7日涨幅',
    key: 'week_change',
    width: 85,
    render: (row) => {
      const color = row.week_change > 0 ? '#f56c6c' : row.week_change < 0 ? '#67c23a' : '#909399'
      return h('div', { style: { fontSize: '10px', lineHeight: '1.2' } }, [
        h('div', { style: { color, fontWeight: 500 } }, `${row.week_change_pct.toFixed(1)}%`),
        h('div', { style: { color: 'var(--n-text-color-3)', fontSize: '9px' } }, row.week_change.toFixed(0))
      ])
    }
  },
  {
    title: '30日价格',
    key: 'month_price',
    width: 70,
    render: (row) => h('div', { style: { fontSize: '11px' } }, row.month_price.toFixed(0))
  },
  {
    title: '30日涨幅',
    key: 'month_change',
    width: 85,
    render: (row) => {
      const color = row.month_change > 0 ? '#f56c6c' : row.month_change < 0 ? '#67c23a' : '#909399'
      return h('div', { style: { fontSize: '10px', lineHeight: '1.2' } }, [
        h('div', { style: { color, fontWeight: 500 } }, `${row.month_change_pct.toFixed(1)}%`),
        h('div', { style: { color: 'var(--n-text-color-3)', fontSize: '9px' } }, row.month_change.toFixed(0))
      ])
    }
  },
  {
    title: '24h区间',
    key: 'range_mobile',
    width: 100,
    render: (row) => {
      const formatTime = (timestamp) => {
        if (!timestamp) return '未知'
        const date = new Date(timestamp)
        return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`
      }
      
      return h('div', { style: { fontSize: '9px', lineHeight: '1.2' } }, [
        h('div', { style: { color: '#f56c6c', marginBottom: '1px' } }, 
          `高${row.highest_price_24h?.toFixed(0) || 0}(${formatTime(row.highest_price_24h_time)})`
        ),
        h('div', { style: { color: '#67c23a' } }, 
          `低${row.lowest_price_24h?.toFixed(0) || 0}(${formatTime(row.lowest_price_24h_time)})`
        )
      ])
    }
  },
  {
    title: '趋势',
    key: 'trend_mobile',
    width: 35,
    render: (row) => {
      const trend = row.price_trend || '平稳'
      const trendColor = trend === '上涨' ? '#f56c6c' : trend === '下跌' ? '#67c23a' : '#909399'
      const trendIcon = trend === '上涨' ? '↗' : trend === '下跌' ? '↘' : '→'
      return h('span', { 
        style: { 
          color: trendColor, 
          fontSize: '12px',
          fontWeight: 500
        } 
      }, trendIcon)
    }
  },
  {
    title: '波动率',
    key: 'volatility_mobile',
    width: 50,
    render: (row) => {
      const volatility = row.volatility_24h || 0
      const color = volatility > 10 ? '#f56c6c' : volatility > 5 ? '#e6a23c' : '#67c23a'
      return h('span', { style: { color, fontSize: '10px' } }, `${volatility.toFixed(1)}%`)
    }
  }
]

// --------------- 方法 ---------------
// 筛选处理函数
const handleFilterChange = () => {
  page.value = 1 // 筛选时重置到第一页
  fetchData()
}

const handlePriceRangeChange = () => {
  // 防抖处理，避免频繁请求
  clearTimeout(priceFilterTimer.value)
  priceFilterTimer.value = setTimeout(() => {
    page.value = 1
    fetchData()
  }, 800)
}

const resetFilters = () => {
  filters.value = {
    grade: -1,
    sort: 'current_price_desc',
    minPrice: null,
    maxPrice: null
  }
  page.value = 1
  fetchData()
}

// 价格筛选防抖定时器
const priceFilterTimer = ref<NodeJS.Timeout | null>(null)

// 获取数据
const fetchData = async () => {
  try {
    loading.value = true
    
    // 构建请求参数
    const params: any = { 
      page: page.value, 
      page_size: pageSize.value,
      sort: filters.value.sort
    }
    
    // 添加等级筛选（-1表示全部，1-6对应数据库的等级）
    if (filters.value.grade > 0) {
      params.grade = filters.value.grade
    }
    
    // 添加价格范围筛选
    if (filters.value.minPrice !== null && filters.value.minPrice > 0) {
      params.min_price = filters.value.minPrice
    }
    if (filters.value.maxPrice !== null && filters.value.maxPrice > 0) {
      params.max_price = filters.value.maxPrice
    }
    
    const resp = await getKeycardRanking(params)
    // 类型断言，兼容后端返回结构
    const data = resp.data as unknown as KeycardRankingResponse
    if (resp.code === 1 && data && Array.isArray(data.list)) {
      // 转换价格数据类型，确保所有价格字段都是数字类型
      const convertedList = data.list.map(item => ({
        ...item,
        current_price: typeof item.current_price === 'string' ? parseFloat(item.current_price) : item.current_price,
        day_price: typeof item.day_price === 'string' ? parseFloat(item.day_price) : item.day_price,
        day_change: typeof item.day_change === 'string' ? parseFloat(item.day_change) : item.day_change,
        day_change_pct: typeof item.day_change_pct === 'string' ? parseFloat(item.day_change_pct) : item.day_change_pct,
        week_price: typeof item.week_price === 'string' ? parseFloat(item.week_price) : item.week_price,
        week_change: typeof item.week_change === 'string' ? parseFloat(item.week_change) : item.week_change,
        week_change_pct: typeof item.week_change_pct === 'string' ? parseFloat(item.week_change_pct) : item.week_change_pct,
        month_price: typeof item.month_price === 'string' ? parseFloat(item.month_price) : item.month_price,
        month_change: typeof item.month_change === 'string' ? parseFloat(item.month_change) : item.month_change,
        month_change_pct: typeof item.month_change_pct === 'string' ? parseFloat(item.month_change_pct) : item.month_change_pct
      }))

      items.value = convertedList
      total.value = data.total
    } else {
      items.value = []
      total.value = 0
    }
  } catch (err) {
    console.error('获取钥匙卡排行榜失败', err)
    items.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 分页变化
const onPageChange = (p: number) => {
  page.value = p
  fetchData()
}

const onPageSizeChange = (s: number) => {
  pageSize.value = s
  page.value = 1
  fetchData()
}

// 显示物品详情
const showItemDetail = (objectId: number) => {
  console.log('KeycardRanking: 点击显示物品详情, objectId:', objectId)
  selectedItemId.value = objectId
  showDetailModal.value = true
  console.log('KeycardRanking: 设置状态完成, selectedItemId:', selectedItemId.value, 'showDetailModal:', showDetailModal.value)
}

// 组件挂载时加载数据
onMounted(() => {
  fetchData()
})
</script>

<style scoped>
.keycard-ranking {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.filter-section {
  background: var(--n-card-color);
  padding: 16px;
  border: 1px solid var(--n-border-color);
  border-radius: 8px;
  margin-bottom: 8px;
}

.filter-item {
  display: flex;
  align-items: center;
}

.header {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.ranking-section {
  background: var(--n-card-color);
  padding: 16px;
  border: 1px solid var(--n-border-color);
  border-radius: 8px;
}

.loading-wrapper,
.empty-wrapper {
  min-height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pagination-wrapper {
  margin-top: 16px;
  display: flex;
  justify-content: center;
}

/* 移动端优化 */
@media (max-width: 768px) {
  .keycard-ranking {
    gap: 12px;
    padding: 0 8px;
  }
  
  .header {
    padding: 12px;
  }
  
  .filter-section {
    padding: 12px;
  }
  
  .filter-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
    margin-bottom: 8px;
  }
  
  .ranking-section {
    padding: 8px;
  }
  
  /* 表格样式优化 */
  :deep(.n-data-table) {
    --n-th-padding: 6px 4px;
    --n-td-padding: 6px 4px;
    font-size: 12px;
  }
  
  :deep(.n-data-table-th) {
    font-weight: 600;
    font-size: 11px;
    background: var(--n-table-header-color);
  }
  
  :deep(.n-data-table-td) {
    border-bottom: 1px solid var(--n-border-color);
    vertical-align: middle;
  }
  
  :deep(.n-data-table-tr:hover .n-data-table-td) {
    background: var(--n-color-hover);
  }
  
  /* 分页样式 */
  .pagination-wrapper {
    margin-top: 16px;
  }
  
  :deep(.n-pagination) {
    --n-item-size: 32px;
    --n-item-padding: 4px 8px;
  }
}
</style> 