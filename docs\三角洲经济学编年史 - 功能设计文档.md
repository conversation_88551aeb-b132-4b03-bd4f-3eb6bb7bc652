# 三角洲经济学编年史 - 功能设计文档

## 1. 功能概述

### 1.1 功能描述
三角洲经济学编年史是一个时间轴展示和编辑功能，用于记录和展示各个赛季的重要经济事件、更新和变动。用户可以查看编年史内容，并通过密码验证进行编辑。

### 1.2 核心特性
- 📅 时间轴形式展示赛季编年史
- ✏️ 支持在线编辑（密码保护）
- 🔐 密码验证编辑权限
- 💾 本地密码记忆功能
- 📱 响应式设计，支持移动端

## 2. 菜单结构设计

### 2.1 菜单层级
```
一级菜单：三角洲经济学编年史
└── 二级菜单：S5赛季编年史
    └── 未来可扩展：S4赛季编年史、S6赛季编年史...
```

### 2.2 路由设计
```typescript
// 路由配置
{
  path: '/economics-history',
  name: 'EconomicsHistory',
  children: [
    {
      path: 's5',
      name: 'S5History',
      component: () => import('@/views/EconomicsHistory/S5History.vue')
    },
    {
      path: 's4', 
      name: 'S4History',
      component: () => import('@/views/EconomicsHistory/S4History.vue')
    }
    // 未来赛季可继续扩展...
  ]
}
```

## 3. 数据库设计

### 3.1 编年史主表 (economics_history)
```sql
CREATE TABLE `economics_history` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `season` varchar(10) NOT NULL COMMENT '赛季标识 (S4, S5, S6...)',
  `event_date` date NOT NULL COMMENT '事件发生日期',
  `event_time` time DEFAULT NULL COMMENT '具体时间（可选）',
  `title` varchar(200) NOT NULL COMMENT '事件标题',
  `description` text COMMENT '事件详细描述',
  `event_type` varchar(50) DEFAULT 'update' COMMENT '事件类型: update, market, policy, bug_fix, etc.',
  `importance_level` tinyint(1) DEFAULT 3 COMMENT '重要程度 1-5, 5最重要',
  `tags` json DEFAULT NULL COMMENT '标签数组',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `is_deleted` tinyint(1) DEFAULT 0,
  PRIMARY KEY (`id`),
  KEY `idx_season_date` (`season`, `event_date`),
  KEY `idx_event_type` (`event_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='经济学编年史主表';
```


## 4. 后端API设计

### 4.1 控制器结构
```php
// app/api/controller/EconomicsHistory.php
class EconomicsHistory extends BaseController
{
    // GET /api/economics-history/{season} - 获取指定赛季编年史
    public function getSeasonHistory($season)
    
    // POST /api/economics-history - 创建编年史事件 (需要密码)
    public function createEvent()
    
    // PUT /api/economics-history/{id} - 更新编年史事件 (需要密码) 
    public function updateEvent($id)
    
    // DELETE /api/economics-history/{id} - 删除编年史事件 (需要密码)
    public function deleteEvent($id)
    
    // POST /api/economics-history/verify-password - 验证编辑密码
    public function verifyPassword()
}
```

### 4.2 核心API接口

#### 4.2.1 获取赛季编年史
```http
GET /api/economics-history/s5

Response:
{
  "code": 200,
  "data": [
    {
      "id": 1,
      "event_date": "2024-01-15",
      "event_time": "14:30:00",
      "title": "S5赛季正式开启",
      "description": "新赛季带来了全新的经济体系...",
      "event_type": "season_start",
      "importance_level": 5,
      "tags": ["赛季更新", "经济调整"]
    }
  ]
}
```

#### 4.2.2 密码验证
```http
POST /api/economics-history/verify-password
Content-Type: application/json

{
  "password": "dfhub1haozi2025"
}

Response:
{
  "code": 200,
  "message": "密码验证成功",
  "data": {
    "token": "edit_token_123456", // 临时编辑token，有效期2小时
    "expires_at": "2024-01-15 16:30:00"
  }
}
```

#### 4.2.3 创建/编辑事件
```http
POST /api/economics-history
Content-Type: application/json
Authorization: Bearer edit_token_123456

{
  "season": "S5",
  "event_date": "2024-01-20",
  "event_time": "10:00:00",
  "title": "物品价格大幅调整",
  "description": "受市场波动影响，多个热门物品价格发生重大变化...",
  "event_type": "market",
  "importance_level": 4,
  "tags": ["价格调整", "市场变动"]
}
```

## 5. 前端组件设计

### 5.1 页面组件结构
```
views/EconomicsHistory/
├── index.vue                    # 主路由组件
├── S5History.vue               # S5赛季编年史页面
├── S4History.vue               # S4赛季编年史页面
└── components/
    ├── TimelineView.vue        # 时间轴展示组件
    ├── EventEditor.vue         # 事件编辑器组件
    ├── PasswordModal.vue       # 密码验证弹窗
    └── EventCard.vue          # 事件卡片组件
```

### 5.2 核心组件设计

#### 5.2.1 时间轴组件 (TimelineView.vue)
```vue
<template>
  <div class="timeline-container">
    <!-- 编辑模式切换按钮 -->
    <div class="timeline-header">
      <h2>{{ seasonTitle }}</h2>
      <n-button 
        v-if="!editMode" 
        @click="enterEditMode"
        type="primary"
        ghost
      >
        进入编辑模式
      </n-button>
      <n-button 
        v-else 
        @click="exitEditMode"
        type="warning"
      >
        退出编辑模式
      </n-button>
    </div>

    <!-- 时间轴 -->
    <n-timeline>
      <n-timeline-item
        v-for="event in timelineData"
        :key="event.id"
        :type="getEventType(event.importance_level)"
        :title="event.title"
        :time="formatEventTime(event)"
      >
        <template #header>
          <div class="event-header">
            <span class="event-title">{{ event.title }}</span>
            <div class="event-actions" v-if="editMode">
              <n-button size="small" @click="editEvent(event)">编辑</n-button>
              <n-button size="small" type="error" @click="deleteEvent(event.id)">删除</n-button>
            </div>
          </div>
        </template>
        
        <EventCard 
          :event="event" 
          :edit-mode="editMode"
          @edit="editEvent"
          @delete="deleteEvent"
        />
      </n-timeline-item>
    </n-timeline>

    <!-- 添加新事件按钮 -->
    <n-button 
      v-if="editMode"
      @click="addNewEvent"
      type="primary"
      class="add-event-btn"
      block
      dashed
    >
      + 添加新事件
    </n-button>

    <!-- 密码验证弹窗 -->
    <PasswordModal 
      v-model:show="showPasswordModal"
      @success="onPasswordSuccess"
    />

    <!-- 事件编辑器 -->
    <EventEditor
      v-model:show="showEventEditor"
      :event="currentEditEvent"
      :season="season"
      @save="onEventSave"
    />
  </div>
</template>
```

#### 5.2.2 密码验证组件 (PasswordModal.vue)
```vue
<template>
  <n-modal v-model:show="visible" :mask-closable="false">
    <n-card
      style="width: 400px"
      title="编辑权限验证"
      :bordered="false"
      size="huge"
    >
      <n-form @submit.prevent="submitPassword">
        <n-form-item label="编辑密码">
          <n-input
            v-model:value="password"
            type="password"
            placeholder="请输入编辑密码"
            show-password-on="mousedown"
            @keyup.enter="submitPassword"
          />
        </n-form-item>
        
        <n-form-item>
          <n-checkbox v-model:checked="rememberPassword">
            记住密码（本地保存）
          </n-checkbox>
        </n-form-item>
        
        <n-form-item>
          <n-button 
            type="primary" 
            @click="submitPassword"
            :loading="loading"
            block
          >
            验证密码
          </n-button>
        </n-form-item>
      </n-form>
    </n-card>
  </n-modal>
</template>
```

## 6. 权限控制方案

### 6.1 编辑权限控制
1. **密码验证机制**
   - 用户点击"进入编辑模式"时触发密码输入弹窗
   - 硬编码密码：`dfhub1haozi2025`
   - 密码验证成功后获得临时编辑token（有效期2小时）
   - token用于后续所有编辑操作的权限验证

2. **本地密码记忆**
   - 用户可选择记住密码（localStorage存储）
   - 下次访问时自动填充密码，提升用户体验
   - 用户可随时清除本地保存的密码

3. **权限状态管理**
   - 使用Pinia store统一管理编辑状态和权限token
   - 自动检查token有效期，过期时自动退出编辑模式
   - 页面刷新时尝试恢复编辑状态（如果token未过期）

4. **密码常量定义**
   ```php
   // 后端常量定义
   class EconomicsHistoryService {
       const EDIT_PASSWORD = 'dfhub1haozi2025';
   }
   ```
   
   ```typescript
   // 前端常量定义（用于本地验证，可选）
   export const ECONOMICS_HISTORY_PASSWORD = 'dfhub1haozi2025'
   ```

### 6.2 查看权限
- 所有用户均可查看编年史内容
- 无需登录即可访问和浏览

## 7. 实现计划

### 7.1 开发阶段
1. **Phase 1**: 数据库表设计和后端API开发
2. **Phase 2**: 前端基础组件和页面结构
3. **Phase 3**: 编辑功能和权限控制
4. **Phase 4**: 优化和测试

### 7.2 技术栈
- **后端**: ThinkPHP 8.1 + MySQL + Redis
- **前端**: Vue 3 + TypeScript + Naive UI + Pinia
- **工具**: Vite + ESLint + Prettier

### 7.3 预期效果
- 直观的时间轴展示各赛季重要事件
- 便捷的编辑功能，支持管理员快速更新内容
- 良好的用户交互体验
- 响应式设计，适配各种设备和屏幕尺寸

---

## 附录：示例数据

### A.1 S5赛季编年史示例数据
```json
[
  {
    "event_date": "2024-01-15",
    "event_time": "14:00:00",
    "title": "S5赛季正式开启",
    "description": "新赛季带来了全新的武器平衡调整和经济系统优化，所有玩家可以体验全新的游戏机制。",
    "event_type": "season_start",
    "importance_level": 5,
    "tags": ["赛季更新", "系统升级"]
  },
  {
    "event_date": "2024-01-20",
    "event_time": "10:30:00", 
    "title": "热门装备价格波动",
    "description": "受市场供需影响，多款热门战术装备价格出现显著变化，部分装备涨幅超过20%。",
    "event_type": "market",
    "importance_level": 4,
    "tags": ["价格变动", "市场波动", "装备"]
  },
  {
    "event_date": "2024-02-01",
    "title": "经济系统热修复",
    "description": "修复了交易系统中的关键漏洞，恢复正常的市场交易功能。",
    "event_type": "bug_fix", 
    "importance_level": 3,
    "tags": ["bug修复", "交易系统"]
  }
]
```

### A.2 菜单配置示例
```typescript
// 在AppLayout.vue中添加新菜单项
const sideMenuOptions = [
  // ... 现有菜单项
  {
    label: '三角洲经济学编年史',
    key: 'economics-history',
    icon: () => h(HistoryIcon),
    children: [
      {
        label: 'S5赛季编年史',
        key: 'economics-history-s5',
        route: '/economics-history/s5'
      },
      {
        label: 'S4赛季编年史', 
        key: 'economics-history-s4',
        route: '/economics-history/s4'
      }
    ]
  }
]
```